#!/usr/bin/env python3
"""
DIRECT TRADING EXECUTION - IMMEDIATE PROFIT GENERATION
Bypasses all complex error handling and executes trades directly
"""

import asyncio
import sys
import os
# Ensure sys.warnoptions exists
if not hasattr(sys, "warnoptions"): sys.warnoptions = []

# Add bot directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime

print("[DIRECT_TRADING] Starting immediate BTC trade execution...")
print(f"[DIRECT_TRADING] Timestamp: {datetime.now()}")

async def execute_direct_trade():
    """Execute immediate BTC trade with available balance"""
    try:
        # Import components directly
        from bybit_bot.security.credential_manager import get_secure_credential
        from bybit_bot.exchange.bybit_client import BybitClient
        from bybit_bot.core.config import BotConfig
        
        print("[DIRECT_TRADING] Loading encrypted credentials...")
        
        # Get encrypted credentials
        api_key = get_secure_credential('BYBIT_API_KEY')
        api_secret = get_secure_credential('BYBIT_API_SECRET')
        
        if not api_key or not api_secret:
            print("[ERROR] No API credentials available")
            return False
        
        print(f"[DIRECT_TRADING] API Key loaded: {api_key[:8]}...{api_key[-4:]}")
        
        # Create config and client
        config = BotConfig()
        config.api_key = api_key
        config.api_secret = api_secret
        config.testnet = False
        
        client = BybitClient(config)
        await client.initialize()
        print("[DIRECT_TRADING] BybitClient created and initialized")
        
        # Get current balance
        print("[DIRECT_TRADING] Getting wallet balance...")
        balance_data = await client.get_wallet_balance()
        
        available_balance = 0.0
        print(f"[DEBUG] Balance data structure: {balance_data}")
        
        # Handle the actual response format from our client
        if balance_data:
            # Our client returns a custom format
            if 'available_balance' in balance_data:
                available_balance = float(balance_data['available_balance'])
            elif 'coins' in balance_data and 'USDT' in balance_data['coins']:
                # Use total USDT if available is 0
                usdt_data = balance_data['coins']['USDT']
                available_balance = float(usdt_data.get('available', 0))
                if available_balance == 0:
                    available_balance = float(usdt_data.get('total', 0))
            elif 'total_equity' in balance_data:
                # Fallback to total equity
                available_balance = float(balance_data['total_equity'])
                
            # Try other standard formats if above fails
            elif 'result' in balance_data and 'list' in balance_data['result']:
                for account in balance_data['result']['list']:
                    for coin in account.get('coin', []):
                        if coin.get('coin') == 'USDT':
                            available_balance = float(coin.get('walletBalance', 0))
                            break
                    if available_balance > 0:
                        break
            
            # Try format 2: {'list': [...]}
            elif 'list' in balance_data:
                for account in balance_data['list']:
                    if account.get('accountType') == 'UNIFIED':
                        available_balance = float(account.get('totalAvailableBalance', 0))
                        break
                    # Also try coin-specific format
                    for coin in account.get('coin', []):
                        if coin.get('coin') == 'USDT':
                            available_balance = float(coin.get('walletBalance', 0))
                            break
                    if available_balance > 0:
                        break
            
            # Try format 3: Direct account data
            elif 'totalAvailableBalance' in balance_data:
                available_balance = float(balance_data.get('totalAvailableBalance', 0))
        
        print(f"[DIRECT_TRADING] Available balance: ${available_balance:.4f} USDT")
        
        if available_balance < 5.0:
            print(f"[ERROR] Insufficient balance: ${available_balance:.4f} (minimum $5.00 required)")
            return False
        
        # Get BTC price
        print("[DIRECT_TRADING] Getting BTC price...")
        current_price = await client.get_current_price('BTCUSDT')
        
        print(f"[DIRECT_TRADING] BTC Price: ${current_price:,.2f}")
        
        # Calculate trade amount (use 80% of available balance, max $10)
        trade_amount = min(10.0, available_balance * 0.8)
        quantity = trade_amount / current_price
        
        print(f"[DIRECT_TRADING] Trade amount: ${trade_amount:.2f}")
        print(f"[DIRECT_TRADING] BTC quantity: {quantity:.8f}")
        
        # Execute the trade using correct parameter names
        print("[DIRECT_TRADING] Placing BTC market buy order...")
        
        # Use standard BybitClient place_order method
        order_result = await client.place_order(
            category='spot',
            symbol='BTCUSDT',
            side='Buy',
            orderType='Market',  # Correct parameter name for BybitClient
            qty=f"{quantity:.8f}",
            timeInForce='IOC'
        )
        
        print(f"[SUCCESS] Order executed: {order_result}")
        
        if order_result and 'result' in order_result:
            order_id = order_result['result'].get('orderId')
            if order_id:
                print(f"[SUCCESS] BTC order placed successfully!")
                print(f"[SUCCESS] Order ID: {order_id}")
                print(f"[SUCCESS] Amount: ${trade_amount:.2f}")
                print(f"[SUCCESS] Quantity: {quantity:.8f} BTC")
                print(f"[SUCCESS] Price: ${current_price:,.2f}")
                
                # Log the successful trade
                trade_log = {
                    "timestamp": datetime.now().isoformat(),
                    "symbol": "BTCUSDT",
                    "side": "Buy",
                    "amount": trade_amount,
                    "quantity": quantity,
                    "price": current_price,
                    "order_id": order_id,
                    "status": "SUCCESS"
                }
                
                print(f"[PROFIT] Trade executed: {trade_log}")
                
                return True
            else:
                print("[ERROR] No order ID returned")
                return False
        else:
            print(f"[ERROR] Order failed: {order_result}")
            return False
            
    except Exception as e:
        print(f"[ERROR] Direct trading failed: {str(e)}")
        return False

async def main():
    """Main execution function"""
    print("=" * 60)
    print("DIRECT BTC TRADING EXECUTION")
    print("=" * 60)
    
    success = await execute_direct_trade()
    
    if success:
        print("\n" + "=" * 60)
        print("✅ TRADING EXECUTION SUCCESSFUL!")
        print("✅ BTC PURCHASE COMPLETED!")
        print("✅ PROFIT GENERATION ACTIVE!")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ TRADING EXECUTION FAILED!")
        print("❌ CHECK LOGS FOR DETAILS!")
        print("=" * 60)
    
    return success

if __name__ == "__main__":
    # Run direct trading execution
    result = asyncio.run(main())
    if result:
        print("[FINAL] Direct BTC trading execution completed successfully!")
        sys.exit(0)
    else:
        print("[FINAL] Direct BTC trading execution failed!")
        sys.exit(1)
