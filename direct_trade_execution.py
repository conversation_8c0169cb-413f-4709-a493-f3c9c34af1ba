#!/usr/bin/env python3
"""
ULTRA-SIMPLE IMMEDIATE TRADE EXECUTION
Direct API calls to force immediate trading activity
"""

import asyncio
import aiohttp
import hmac
import hashlib
import time
import json
from urllib.parse import urlencode

class DirectBybitTrader:
    def __init__(self):
        self.api_key = "WbQDRvmEzuiNyDI0QyxN3nXQEj"
        self.api_secret = "vdviKwMlqfCUy9AJ7V0hVLKXMGaWgMYyFUga"
        self.base_url = "https://api.bybit.com"
        
    def create_signature(self, method, endpoint, params, timestamp):
        """Create API signature"""
        recv_window = "5000"
        
        if method == "GET":
            query_string = urlencode(sorted(params.items())) if params else ""
            param_str = f"{timestamp}{self.api_key}{recv_window}{query_string}"
        else:
            body = json.dumps(params, separators=(',', ':')) if params else ""
            param_str = f"{timestamp}{self.api_key}{recv_window}{body}"
        
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            param_str.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    async def make_request(self, method, endpoint, params=None):
        """Make authenticated API request"""
        timestamp = str(int(time.time() * 1000))
        
        headers = {
            "X-BAPI-API-KEY": self.api_key,
            "X-BAPI-TIMESTAMP": timestamp,
            "X-BAPI-RECV-WINDOW": "5000",
            "X-BAPI-SIGN-TYPE": "2",
            "Content-Type": "application/json"
        }
        
        signature = self.create_signature(method, endpoint, params, timestamp)
        headers["X-BAPI-SIGN"] = signature
        
        url = f"{self.base_url}{endpoint}"
        
        async with aiohttp.ClientSession() as session:
            if method == "GET":
                if params:
                    url += f"?{urlencode(sorted(params.items()))}"
                async with session.get(url, headers=headers) as response:
                    return await response.json()
            else:
                data = json.dumps(params) if params else "{}"
                async with session.post(url, headers=headers, data=data) as response:
                    return await response.json()
    
    async def get_account_balance(self):
        """Get account balance"""
        try:
            params = {"accountType": "UNIFIED"}
            result = await self.make_request("GET", "/v5/account/wallet-balance", params)
            print(f"[DEBUG] Balance API response: {result}")
            return result
        except Exception as e:
            print(f"[ERROR] Balance request failed: {e}")
            return None
    
    async def get_current_price(self, symbol):
        """Get current price"""
        url = f"{self.base_url}/v5/market/tickers"
        params = {"category": "linear", "symbol": symbol}
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                data = await response.json()
                if data.get("retCode") == 0 and data["result"]["list"]:
                    return float(data["result"]["list"][0]["lastPrice"])
        return 0
    
    async def place_order(self, symbol, side, qty, order_type="Market", price=None):
        """Place order"""
        params = {
            "category": "linear",
            "symbol": symbol,
            "side": side,
            "orderType": order_type,
            "qty": str(qty)
        }
        
        if price:
            params["price"] = str(price)
        
        return await self.make_request("POST", "/v5/order/create", params)
    
    async def execute_test_trade(self):
        """Execute a small test trade"""
        try:
            print("=" * 60)
            print("🚀 DIRECT BYBIT TRADING TEST")
            print("=" * 60)
            
            # Check balance
            print("\n📊 Checking account balance...")
            balance_result = await self.get_account_balance()
            
            if balance_result is None:
                print("❌ Failed to get balance response")
                return False
            
            if balance_result.get("retCode") == 0:
                coins = balance_result["result"]["list"][0]["coin"]
                usdt_balance = next((coin for coin in coins if coin["coin"] == "USDT"), None)
                
                if usdt_balance:
                    available = float(usdt_balance["availableToWithdraw"])
                    print(f"Available USDT: ${available:.2f}")
                    
                    if available < 5:
                        print("❌ Insufficient funds for trading")
                        return False
                else:
                    print("❌ No USDT balance found")
                    return False
            else:
                print(f"❌ Failed to get balance: {balance_result}")
                return False
            
            # Get BTC price
            print("\n📈 Getting BTC price...")
            btc_price = await self.get_current_price("BTCUSDT")
            
            if btc_price > 0:
                print(f"BTC Price: ${btc_price:.2f}")
            else:
                print("❌ Failed to get BTC price")
                return False
            
            # Calculate trade size (very small test trade)
            trade_value = min(5.0, available * 0.1)  # $5 or 10% of balance, whichever is smaller
            qty = trade_value / btc_price
            
            # Round to minimum quantity
            qty = round(qty, 6)
            
            print(f"\n🎯 Executing test trade...")
            print(f"Trade Value: ${trade_value:.2f}")
            print(f"Quantity: {qty:.6f} BTC")
            
            if qty < 0.001:  # Minimum BTC quantity
                print("❌ Quantity too small for minimum order")
                return False
            
            # Place buy order
            print("\n📥 Placing BUY order...")
            buy_result = await self.place_order("BTCUSDT", "Buy", qty)
            
            if buy_result.get("retCode") == 0:
                order_id = buy_result["result"]["orderId"]
                print(f"✅ BUY ORDER PLACED - ID: {order_id}")
                
                # Wait a moment
                await asyncio.sleep(2)
                
                # Place sell order immediately (quick scalp)
                sell_price = btc_price * 1.002  # 0.2% profit target
                print(f"\n📤 Placing SELL order at ${sell_price:.2f}...")
                
                sell_result = await self.place_order("BTCUSDT", "Sell", qty, "Limit", sell_price)
                
                if sell_result.get("retCode") == 0:
                    sell_order_id = sell_result["result"]["orderId"]
                    print(f"✅ SELL ORDER PLACED - ID: {sell_order_id}")
                    print("\n🎉 TRADING ACTIVITY GENERATED!")
                    print("💰 Profit target: 0.2%")
                    return True
                else:
                    print(f"❌ Sell order failed: {sell_result}")
                    return False
            else:
                print(f"❌ Buy order failed: {buy_result}")
                return False
                
        except Exception as e:
            print(f"❌ Trading failed: {e}")
            return False

async def main():
    trader = DirectBybitTrader()
    
    print("🚀 STARTING DIRECT BYBIT TRADING")
    print("⚡ IMMEDIATE EXECUTION - REAL MONEY")
    print("🎯 GENERATING ACCOUNT ACTIVITY")
    
    success = await trader.execute_test_trade()
    
    if success:
        print("\n" + "=" * 60)
        print("✅ TRADING ACTIVITY SUCCESSFULLY GENERATED!")
        print("📈 Check your Bybit account for order history")
        print("💹 Live profit generation is now active")
        print("=" * 60)
    else:
        print("\n❌ Failed to generate trading activity")

if __name__ == "__main__":
    asyncio.run(main())
