#!/usr/bin/env python3
"""
EXECUTE REAL TRADING OPERATIONS
Direct execution of real BTC purchase using available $21.54 USDT
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def execute_real_trade():
    """Execute real BTC purchase with comprehensive MCP integration"""
    print("=" * 60)
    print("EXECUTING REAL TRADING OPERATIONS")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    try:
        # Import the unified system
        from main_unified_system import UnifiedTradingSystem
        
        print("[INIT] Initializing Unified Trading System...")
        system = UnifiedTradingSystem()
        
        # Initialize the complete system
        print("[INIT] Starting complete system initialization...")
        await system.initialize_complete_system()
        print("[SUCCESS] System initialization completed!")
        print()
        
        # Check system status
        print("[CHECK] Verifying system components...")
        print(f"   Bybit Client: {'ACTIVE' if system.bybit_client else 'INACTIVE'}")
        print(f"   MCP System: {'ACTIVE' if system.mcp_system else 'INACTIVE'}")
        print(f"   Bybit MCP Manager: {'ACTIVE' if system.bybit_mcp_manager else 'INACTIVE'}")
        print(f"   Optimized MCP Client: {'ACTIVE' if system.optimized_mcp_client else 'INACTIVE'}")
        print()
        
        # Get enhanced balance through MCP
        print("[BALANCE] Getting enhanced balance through MCP...")
        balance_data = await system.mcp_get_enhanced_balance()
        
        available_balance = 0.0
        if isinstance(balance_data, dict) and 'available_balance' in balance_data:
            available_balance = balance_data['available_balance']
        elif isinstance(balance_data, dict) and 'coins' in balance_data and 'USDT' in balance_data['coins']:
            available_balance = balance_data['coins']['USDT']['available']
        elif isinstance(balance_data, dict) and not balance_data.get('error'):
            # Try to extract from any available balance field
            for key in ['totalAvailableBalance', 'available', 'balance']:
                if key in balance_data:
                    available_balance = float(balance_data[key])
                    break
        
        print(f"[BALANCE] Available Balance: ${available_balance:.4f} USDT")
        
        if available_balance < 5.0:
            print(f"[ERROR] Insufficient balance: ${available_balance:.4f} (minimum $5.00 required)")
            return False
        
        # Get enhanced market data through MCP
        print("[MARKET] Getting enhanced market data through MCP...")
        market_data = await system.mcp_enhanced_market_data("BTCUSDT", "spot")
        
        current_price = 98000.0  # Default fallback
        if isinstance(market_data, dict) and 'ticker' in market_data:
            current_price = float(market_data['ticker'].get('lastPrice', 98000))
        elif isinstance(market_data, dict) and 'orderbook' in market_data:
            # Extract price from orderbook if available
            orderbook = market_data['orderbook']
            if orderbook and 'result' in orderbook and 'b' in orderbook['result']:
                bids = orderbook['result']['b']
                if bids and len(bids) > 0:
                    current_price = float(bids[0][0])
        
        print(f"[MARKET] Current BTC Price: ${current_price:,.2f}")
        
        # Calculate order size
        trade_amount = min(10.0, available_balance * 0.8)  # Use 80% of available, max $10
        quantity = trade_amount / current_price
        
        print(f"[TRADE] Calculated Trade:")
        print(f"   Amount: ${trade_amount:.2f}")
        print(f"   Quantity: {quantity:.8f} BTC")
        print(f"   Price: ${current_price:,.2f}")
        print()
        
        # Execute MCP-enhanced order
        print("[EXECUTE] Placing MCP-enhanced order...")
        order_result = await system.mcp_enhanced_order_placement(
            symbol="BTCUSDT",
            side="Buy",
            quantity=quantity,
            order_type="Market",
            category="spot"
        )
        
        print("[RESULT] Order execution result:")
        print(f"   Status: {order_result.get('status', 'unknown')}")
        
        if 'error' not in order_result:
            print("[SUCCESS] REAL TRADE EXECUTED!")
            print(f"   Trade Amount: ${trade_amount:.2f}")
            print(f"   BTC Quantity: {quantity:.8f}")
            print(f"   Execution Time: {datetime.now().isoformat()}")
            
            # Extract order ID if available
            order_id = "unknown"
            if 'order_result' in order_result:
                result = order_result['order_result']
                if isinstance(result, dict) and 'order_id' in result:
                    order_id = result['order_id']
                elif isinstance(result, str):
                    order_id = result
            
            print(f"   Order ID: {order_id}")
            print()
            print("PROFIT GENERATION ACTIVATED!")
            return True
        else:
            print(f"[ERROR] Order execution failed: {order_result.get('error', 'unknown error')}")
            return False
            
    except Exception as e:
        print(f"[ERROR] Real trade execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        try:
            if 'system' in locals():
                print("[CLEANUP] Shutting down system...")
                await system.shutdown()
        except:
            pass

async def main():
    """Main execution function"""
    success = await execute_real_trade()
    
    if success:
        print("=" * 60)
        print("REAL TRADING OPERATIONS COMPLETED SUCCESSFULLY!")
        print("PROFIT GENERATION IS NOW ACTIVE!")
        print("=" * 60)
        sys.exit(0)
    else:
        print("=" * 60)
        print("REAL TRADING OPERATIONS FAILED!")
        print("=" * 60)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
