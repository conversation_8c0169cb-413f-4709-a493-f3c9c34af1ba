"""
MULTI-CATEGORY TRADING SYSTEM
Strategy-based switching between spot, linear, and options trading based on real-time market conditions
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class TradingCategory(Enum):
    SPOT = "spot"
    LINEAR = "linear"
    OPTION = "option"

class MarketCondition(Enum):
    BULLISH = "bullish"
    BEARISH = "bearish"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    LOW_VOLATILITY = "low_volatility"

class TradingStrategy(Enum):
    SPOT_ACCUMULATION = "spot_accumulation"
    LINEAR_MOMENTUM = "linear_momentum"
    OPTION_VOLATILITY = "option_volatility"
    ARBITRAGE = "arbitrage"
    HEDGE = "hedge"

@dataclass
class CategoryAnalysis:
    category: TradingCategory
    suitability_score: float
    expected_profit: float
    risk_level: float
    market_condition: MarketCondition
    recommended_strategy: TradingStrategy
    confidence: float
    reasons: List[str]

@dataclass
class TradingOpportunity:
    category: TradingCategory
    symbol: str
    strategy: TradingStrategy
    entry_price: float
    target_price: float
    stop_loss: float
    position_size: float
    expected_profit: float
    risk_reward_ratio: float
    confidence: float
    expiry_time: Optional[datetime] = None

class MultiCategoryTradingManager:
    """Manages trading across spot, linear, and options categories"""
    
    def __init__(self, bybit_client, config):
        self.bybit_client = bybit_client
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Current market analysis
        self.market_analysis = {}
        self.category_preferences = {}
        self.active_strategies = {}
        
        # Performance tracking
        self.category_performance = {
            TradingCategory.SPOT: {"profit": 0.0, "trades": 0, "win_rate": 0.0},
            TradingCategory.LINEAR: {"profit": 0.0, "trades": 0, "win_rate": 0.0},
            TradingCategory.OPTION: {"profit": 0.0, "trades": 0, "win_rate": 0.0}
        }
        
        # Strategy parameters
        self.volatility_threshold = 0.02  # 2% volatility threshold
        self.trend_strength_threshold = 0.6
        self.min_profit_threshold = 0.005  # 0.5% minimum profit
        
    # =====================================
    # MARKET CONDITION ANALYSIS
    # =====================================
    
    async def analyze_market_conditions(self, symbol: str = "BTCUSDT") -> Dict[str, Any]:
        """Analyze current market conditions for multi-category trading"""
        try:
            # Get market data from all categories
            spot_data = await self._get_market_data(symbol, TradingCategory.SPOT)
            linear_data = await self._get_market_data(symbol, TradingCategory.LINEAR)
            option_data = await self._get_market_data(symbol, TradingCategory.OPTION)
            
            # Analyze volatility
            volatility_analysis = await self._analyze_volatility(symbol, spot_data, linear_data)
            
            # Analyze trend
            trend_analysis = await self._analyze_trend(symbol, spot_data, linear_data)
            
            # Analyze liquidity
            liquidity_analysis = await self._analyze_liquidity(symbol, spot_data, linear_data)
            
            # Determine overall market condition
            market_condition = await self._determine_market_condition(
                volatility_analysis, trend_analysis, liquidity_analysis
            )
            
            analysis = {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "market_condition": market_condition.value,
                "volatility": volatility_analysis,
                "trend": trend_analysis,
                "liquidity": liquidity_analysis,
                "spot_data": spot_data,
                "linear_data": linear_data,
                "option_data": option_data
            }
            
            self.market_analysis[symbol] = analysis
            return analysis
            
        except Exception as e:
            self.logger.error(f"Market condition analysis failed for {symbol}: {e}")
            return {"error": str(e)}
    
    async def _get_market_data(self, symbol: str, category: TradingCategory) -> Dict[str, Any]:
        """Get market data for specific category"""
        try:
            if category == TradingCategory.SPOT:
                # Get spot market data
                ticker = await self.bybit_client.get_current_price(symbol)
                return {
                    "price": ticker,
                    "category": category.value,
                    "timestamp": datetime.now().isoformat()
                }
            
            elif category == TradingCategory.LINEAR:
                # Get linear/futures market data
                ticker = await self.bybit_client.get_current_price(symbol)
                return {
                    "price": ticker,
                    "category": category.value,
                    "timestamp": datetime.now().isoformat()
                }
            
            elif category == TradingCategory.OPTION:
                # Get options market data (if available)
                return {
                    "price": None,
                    "category": category.value,
                    "available": False,
                    "timestamp": datetime.now().isoformat()
                }
            
            return {}
            
        except Exception as e:
            self.logger.error(f"Failed to get {category.value} market data for {symbol}: {e}")
            return {"error": str(e), "category": category.value}
    
    async def _analyze_volatility(self, symbol: str, spot_data: Dict, linear_data: Dict) -> Dict[str, Any]:
        """Analyze market volatility"""
        try:
            # Calculate volatility metrics
            spot_price = spot_data.get("price", 0)
            linear_price = linear_data.get("price", 0)
            
            # Simple volatility calculation (in real implementation, would use historical data)
            price_diff = abs(spot_price - linear_price) / spot_price if spot_price > 0 else 0
            
            volatility_level = "high" if price_diff > self.volatility_threshold else "low"
            
            return {
                "volatility_level": volatility_level,
                "price_difference": price_diff,
                "spot_price": spot_price,
                "linear_price": linear_price,
                "suitable_for_options": price_diff > self.volatility_threshold
            }
            
        except Exception as e:
            self.logger.error(f"Volatility analysis failed: {e}")
            return {"error": str(e)}
    
    async def _analyze_trend(self, symbol: str, spot_data: Dict, linear_data: Dict) -> Dict[str, Any]:
        """Analyze market trend"""
        try:
            # Simple trend analysis (in real implementation, would use technical indicators)
            spot_price = spot_data.get("price", 0)
            linear_price = linear_data.get("price", 0)
            
            # Determine trend direction
            if linear_price > spot_price * 1.001:  # 0.1% premium
                trend_direction = "bullish"
                trend_strength = min((linear_price - spot_price) / spot_price * 100, 1.0)
            elif linear_price < spot_price * 0.999:  # 0.1% discount
                trend_direction = "bearish"
                trend_strength = min((spot_price - linear_price) / spot_price * 100, 1.0)
            else:
                trend_direction = "sideways"
                trend_strength = 0.1
            
            return {
                "trend_direction": trend_direction,
                "trend_strength": trend_strength,
                "suitable_for_momentum": trend_strength > self.trend_strength_threshold
            }
            
        except Exception as e:
            self.logger.error(f"Trend analysis failed: {e}")
            return {"error": str(e)}
    
    async def _analyze_liquidity(self, symbol: str, spot_data: Dict, linear_data: Dict) -> Dict[str, Any]:
        """Analyze market liquidity"""
        try:
            # Simple liquidity analysis
            spot_available = spot_data.get("price") is not None
            linear_available = linear_data.get("price") is not None
            
            liquidity_score = 0.5 if spot_available else 0.0
            liquidity_score += 0.5 if linear_available else 0.0
            
            return {
                "liquidity_score": liquidity_score,
                "spot_available": spot_available,
                "linear_available": linear_available,
                "sufficient_liquidity": liquidity_score >= 0.5
            }
            
        except Exception as e:
            self.logger.error(f"Liquidity analysis failed: {e}")
            return {"error": str(e)}
    
    async def _determine_market_condition(self, volatility: Dict, trend: Dict, liquidity: Dict) -> MarketCondition:
        """Determine overall market condition"""
        try:
            volatility_level = volatility.get("volatility_level", "low")
            trend_direction = trend.get("trend_direction", "sideways")
            trend_strength = trend.get("trend_strength", 0)
            
            if volatility_level == "high":
                return MarketCondition.VOLATILE
            elif trend_direction == "bullish" and trend_strength > 0.5:
                return MarketCondition.BULLISH
            elif trend_direction == "bearish" and trend_strength > 0.5:
                return MarketCondition.BEARISH
            elif volatility_level == "low":
                return MarketCondition.LOW_VOLATILITY
            else:
                return MarketCondition.SIDEWAYS
                
        except Exception as e:
            self.logger.error(f"Market condition determination failed: {e}")
            return MarketCondition.SIDEWAYS
    
    # =====================================
    # CATEGORY SELECTION AND STRATEGY
    # =====================================
    
    async def select_optimal_category(self, symbol: str = "BTCUSDT") -> CategoryAnalysis:
        """Select optimal trading category based on market conditions"""
        try:
            # Get market analysis
            market_analysis = await self.analyze_market_conditions(symbol)
            market_condition = MarketCondition(market_analysis.get("market_condition", "sideways"))
            
            # Analyze each category
            spot_analysis = await self._analyze_category_suitability(
                TradingCategory.SPOT, market_condition, market_analysis
            )
            linear_analysis = await self._analyze_category_suitability(
                TradingCategory.LINEAR, market_condition, market_analysis
            )
            option_analysis = await self._analyze_category_suitability(
                TradingCategory.OPTION, market_condition, market_analysis
            )
            
            # Select best category
            categories = [spot_analysis, linear_analysis, option_analysis]
            best_category = max(categories, key=lambda x: x.suitability_score)
            
            self.logger.info(f"Optimal category for {symbol}: {best_category.category.value} (score: {best_category.suitability_score:.2f})")
            return best_category
            
        except Exception as e:
            self.logger.error(f"Category selection failed for {symbol}: {e}")
            # Return default spot analysis
            return CategoryAnalysis(
                category=TradingCategory.SPOT,
                suitability_score=0.5,
                expected_profit=0.01,
                risk_level=0.3,
                market_condition=MarketCondition.SIDEWAYS,
                recommended_strategy=TradingStrategy.SPOT_ACCUMULATION,
                confidence=0.5,
                reasons=["Default fallback"]
            )
    
    async def _analyze_category_suitability(self, category: TradingCategory, 
                                          market_condition: MarketCondition, 
                                          market_analysis: Dict[str, Any]) -> CategoryAnalysis:
        """Analyze suitability of a specific category"""
        try:
            volatility = market_analysis.get("volatility", {})
            trend = market_analysis.get("trend", {})
            liquidity = market_analysis.get("liquidity", {})
            
            if category == TradingCategory.SPOT:
                return await self._analyze_spot_suitability(market_condition, volatility, trend, liquidity)
            elif category == TradingCategory.LINEAR:
                return await self._analyze_linear_suitability(market_condition, volatility, trend, liquidity)
            elif category == TradingCategory.OPTION:
                return await self._analyze_option_suitability(market_condition, volatility, trend, liquidity)
            
            return CategoryAnalysis(
                category=category,
                suitability_score=0.0,
                expected_profit=0.0,
                risk_level=1.0,
                market_condition=market_condition,
                recommended_strategy=TradingStrategy.SPOT_ACCUMULATION,
                confidence=0.0,
                reasons=["Unknown category"]
            )
            
        except Exception as e:
            self.logger.error(f"Category suitability analysis failed: {e}")
            return CategoryAnalysis(
                category=category,
                suitability_score=0.0,
                expected_profit=0.0,
                risk_level=1.0,
                market_condition=market_condition,
                recommended_strategy=TradingStrategy.SPOT_ACCUMULATION,
                confidence=0.0,
                reasons=[f"Analysis error: {str(e)}"]
            )

    async def _analyze_spot_suitability(self, market_condition: MarketCondition,
                                       volatility: Dict, trend: Dict, liquidity: Dict) -> CategoryAnalysis:
        """Analyze spot trading suitability"""
        try:
            suitability_score = 0.6  # Base score for spot
            expected_profit = 0.01   # 1% expected profit
            risk_level = 0.3         # Low risk
            reasons = []

            # Adjust based on market conditions
            if market_condition == MarketCondition.LOW_VOLATILITY:
                suitability_score += 0.2
                reasons.append("Low volatility favors spot accumulation")
            elif market_condition == MarketCondition.BULLISH:
                suitability_score += 0.15
                expected_profit += 0.005
                reasons.append("Bullish trend supports spot buying")
            elif market_condition == MarketCondition.VOLATILE:
                suitability_score -= 0.1
                risk_level += 0.1
                reasons.append("High volatility increases spot risk")

            # Check liquidity
            if liquidity.get("spot_available", False):
                suitability_score += 0.1
                reasons.append("Spot market available")
            else:
                suitability_score -= 0.3
                reasons.append("Spot market not available")

            return CategoryAnalysis(
                category=TradingCategory.SPOT,
                suitability_score=min(suitability_score, 1.0),
                expected_profit=expected_profit,
                risk_level=min(risk_level, 1.0),
                market_condition=market_condition,
                recommended_strategy=TradingStrategy.SPOT_ACCUMULATION,
                confidence=0.8,
                reasons=reasons
            )

        except Exception as e:
            self.logger.error(f"Spot suitability analysis failed: {e}")
            return CategoryAnalysis(
                category=TradingCategory.SPOT,
                suitability_score=0.5,
                expected_profit=0.01,
                risk_level=0.3,
                market_condition=market_condition,
                recommended_strategy=TradingStrategy.SPOT_ACCUMULATION,
                confidence=0.5,
                reasons=["Analysis error"]
            )

    async def _analyze_linear_suitability(self, market_condition: MarketCondition,
                                         volatility: Dict, trend: Dict, liquidity: Dict) -> CategoryAnalysis:
        """Analyze linear/futures trading suitability"""
        try:
            suitability_score = 0.5  # Base score for linear
            expected_profit = 0.015  # 1.5% expected profit
            risk_level = 0.5         # Medium risk
            reasons = []

            # Adjust based on market conditions
            if market_condition == MarketCondition.BULLISH:
                suitability_score += 0.3
                expected_profit += 0.01
                reasons.append("Strong bullish trend ideal for linear long")
            elif market_condition == MarketCondition.BEARISH:
                suitability_score += 0.25
                expected_profit += 0.008
                reasons.append("Bearish trend good for linear short")
            elif market_condition == MarketCondition.VOLATILE:
                suitability_score += 0.2
                expected_profit += 0.005
                risk_level += 0.2
                reasons.append("Volatility creates linear opportunities")
            elif market_condition == MarketCondition.SIDEWAYS:
                suitability_score -= 0.1
                reasons.append("Sideways market less suitable for linear")

            # Check trend strength
            if trend.get("suitable_for_momentum", False):
                suitability_score += 0.15
                expected_profit += 0.005
                reasons.append("Strong momentum supports linear trading")

            # Check liquidity
            if liquidity.get("linear_available", False):
                suitability_score += 0.1
                reasons.append("Linear market available")
            else:
                suitability_score -= 0.3
                reasons.append("Linear market not available")

            return CategoryAnalysis(
                category=TradingCategory.LINEAR,
                suitability_score=min(suitability_score, 1.0),
                expected_profit=expected_profit,
                risk_level=min(risk_level, 1.0),
                market_condition=market_condition,
                recommended_strategy=TradingStrategy.LINEAR_MOMENTUM,
                confidence=0.75,
                reasons=reasons
            )

        except Exception as e:
            self.logger.error(f"Linear suitability analysis failed: {e}")
            return CategoryAnalysis(
                category=TradingCategory.LINEAR,
                suitability_score=0.5,
                expected_profit=0.015,
                risk_level=0.5,
                market_condition=market_condition,
                recommended_strategy=TradingStrategy.LINEAR_MOMENTUM,
                confidence=0.5,
                reasons=["Analysis error"]
            )

    async def _analyze_option_suitability(self, market_condition: MarketCondition,
                                         volatility: Dict, trend: Dict, liquidity: Dict) -> CategoryAnalysis:
        """Analyze options trading suitability"""
        try:
            suitability_score = 0.3  # Base score for options (lower due to complexity)
            expected_profit = 0.02   # 2% expected profit
            risk_level = 0.7         # High risk
            reasons = []

            # Adjust based on market conditions
            if market_condition == MarketCondition.VOLATILE:
                suitability_score += 0.4
                expected_profit += 0.015
                reasons.append("High volatility excellent for options")
            elif market_condition == MarketCondition.LOW_VOLATILITY:
                suitability_score -= 0.2
                expected_profit -= 0.005
                reasons.append("Low volatility reduces options profitability")

            # Check volatility suitability
            if volatility.get("suitable_for_options", False):
                suitability_score += 0.2
                expected_profit += 0.01
                reasons.append("Volatility level suitable for options")
            else:
                suitability_score -= 0.1
                reasons.append("Insufficient volatility for options")

            # Options typically not available on Bybit for most retail traders
            suitability_score -= 0.3
            reasons.append("Options trading limited availability")

            return CategoryAnalysis(
                category=TradingCategory.OPTION,
                suitability_score=max(suitability_score, 0.0),
                expected_profit=expected_profit,
                risk_level=min(risk_level, 1.0),
                market_condition=market_condition,
                recommended_strategy=TradingStrategy.OPTION_VOLATILITY,
                confidence=0.6,
                reasons=reasons
            )

        except Exception as e:
            self.logger.error(f"Option suitability analysis failed: {e}")
            return CategoryAnalysis(
                category=TradingCategory.OPTION,
                suitability_score=0.1,
                expected_profit=0.02,
                risk_level=0.7,
                market_condition=market_condition,
                recommended_strategy=TradingStrategy.OPTION_VOLATILITY,
                confidence=0.3,
                reasons=["Analysis error"]
            )

    # =====================================
    # TRADING EXECUTION
    # =====================================

    async def execute_multi_category_trade(self, symbol: str = "BTCUSDT",
                                          amount: float = 10.0) -> Dict[str, Any]:
        """Execute trade using optimal category selection"""
        try:
            # Select optimal category
            category_analysis = await self.select_optimal_category(symbol)

            # Generate trading opportunity
            opportunity = await self._generate_trading_opportunity(
                symbol, category_analysis, amount
            )

            # Execute trade
            execution_result = await self._execute_trade(opportunity)

            # Update performance tracking
            await self._update_performance_tracking(category_analysis.category, execution_result)

            return {
                "status": "success",
                "category_analysis": {
                    "selected_category": category_analysis.category.value,
                    "suitability_score": category_analysis.suitability_score,
                    "expected_profit": category_analysis.expected_profit,
                    "risk_level": category_analysis.risk_level,
                    "confidence": category_analysis.confidence,
                    "reasons": category_analysis.reasons
                },
                "trading_opportunity": {
                    "symbol": opportunity.symbol,
                    "strategy": opportunity.strategy.value,
                    "entry_price": opportunity.entry_price,
                    "position_size": opportunity.position_size,
                    "expected_profit": opportunity.expected_profit,
                    "risk_reward_ratio": opportunity.risk_reward_ratio
                },
                "execution_result": execution_result,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Multi-category trade execution failed: {e}")
            return {"error": str(e)}

    async def _generate_trading_opportunity(self, symbol: str,
                                           category_analysis: CategoryAnalysis,
                                           amount: float) -> TradingOpportunity:
        """Generate trading opportunity based on category analysis"""
        try:
            # Get current price
            current_price = await self.bybit_client.get_current_price(symbol)

            # Calculate position size
            position_size = amount / current_price

            # Set targets based on category and strategy
            if category_analysis.category == TradingCategory.SPOT:
                target_price = current_price * (1 + category_analysis.expected_profit)
                stop_loss = current_price * 0.98  # 2% stop loss
            elif category_analysis.category == TradingCategory.LINEAR:
                target_price = current_price * (1 + category_analysis.expected_profit)
                stop_loss = current_price * 0.97  # 3% stop loss
            else:  # OPTIONS
                target_price = current_price * (1 + category_analysis.expected_profit)
                stop_loss = current_price * 0.95  # 5% stop loss

            # Calculate risk-reward ratio
            profit_potential = target_price - current_price
            loss_potential = current_price - stop_loss
            risk_reward_ratio = profit_potential / loss_potential if loss_potential > 0 else 0

            return TradingOpportunity(
                category=category_analysis.category,
                symbol=symbol,
                strategy=category_analysis.recommended_strategy,
                entry_price=current_price,
                target_price=target_price,
                stop_loss=stop_loss,
                position_size=position_size,
                expected_profit=category_analysis.expected_profit * amount,
                risk_reward_ratio=risk_reward_ratio,
                confidence=category_analysis.confidence
            )

        except Exception as e:
            self.logger.error(f"Trading opportunity generation failed: {e}")
            raise

    async def _execute_trade(self, opportunity: TradingOpportunity) -> Dict[str, Any]:
        """Execute the trading opportunity"""
        try:
            # Execute order based on category
            result = await self.bybit_client.place_order(
                symbol=opportunity.symbol,
                side="Buy",
                order_type="Market",
                quantity=opportunity.position_size,
                category=opportunity.category.value
            )

            return {
                "success": True,
                "order_result": result,
                "category": opportunity.category.value,
                "strategy": opportunity.strategy.value,
                "execution_time": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Trade execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "category": opportunity.category.value,
                "strategy": opportunity.strategy.value
            }

    async def _update_performance_tracking(self, category: TradingCategory,
                                          execution_result: Dict[str, Any]) -> None:
        """Update performance tracking for the category"""
        try:
            if execution_result.get("success", False):
                self.category_performance[category]["trades"] += 1
                # In real implementation, would track actual profit/loss

        except Exception as e:
            self.logger.error(f"Performance tracking update failed: {e}")

    # =====================================
    # PERFORMANCE AND MONITORING
    # =====================================

    async def get_multi_category_performance(self) -> Dict[str, Any]:
        """Get comprehensive multi-category performance summary"""
        try:
            return {
                "category_performance": {
                    category.value: performance
                    for category, performance in self.category_performance.items()
                },
                "market_analysis": self.market_analysis,
                "active_strategies": {
                    strategy.value: True for strategy in self.active_strategies
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Performance summary failed: {e}")
            return {"error": str(e)}
