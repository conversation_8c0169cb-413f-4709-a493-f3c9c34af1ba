#!/usr/bin/env python3
"""
DEBUG BYBIT CLIENT TEST
Test the exact same request that BybitClient makes
"""

import asyncio
import aiohttp
import json
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_manager import ConfigManager

async def debug_bybit_client():
    """Debug the exact request that BybitClient makes"""
    
    # Get credentials (though we won't use them for market data)
    config_manager = ConfigManager()
    exchange_config = config_manager.get_exchange_config()
    
    print(f"[INFO] Testing with same parameters as BybitClient")
    
    # Exact same parameters as BybitClient
    base_url = "https://api.bybit.com"
    endpoint = "/v5/market/tickers"
    symbol = "BTCUSDT"
    
    categories = ["linear", "spot"]
    
    # Test each category
    for category in categories:
        print(f"\n[TEST] Category: {category}")
        
        params = {
            "category": category,
            "symbol": symbol
        }
        
        url = f"{base_url}{endpoint}"
        
        # Same headers as BybitClient
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Accept-Encoding": "gzip, deflate",
            "User-Agent": "UltraAdvancedTradingBot/4.0"
        }
        
        print(f"URL: {url}")
        print(f"Params: {params}")
        print(f"Headers: {headers}")
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(url, params=params, headers=headers) as response:
                    data = await response.json()
                    
                    print(f"Status: {response.status}")
                    print(f"Response: {json.dumps(data, indent=2)[:500]}...")
                    
                    if data.get("retCode") == 0:
                        if data["result"]["list"]:
                            ticker = data["result"]["list"][0]
                            price = float(ticker["lastPrice"])
                            print(f"[OK] Success! Price: {price}")
                        else:
                            print(f"[WARNING] Success but no data")
                    else:
                        print(f"[ERROR] retCode: {data.get('retCode')}, retMsg: {data.get('retMsg')}")
                        
            except Exception as e:
                print(f"[ERROR] Exception: {e}")

if __name__ == "__main__":
    asyncio.run(debug_bybit_client())
