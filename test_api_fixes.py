#!/usr/bin/env python3
"""
TEST REAL BALANCE RETRIEVAL
Test if the API fix resolves the balance and trading issues
"""

import asyncio
import sys
# Ensure sys.warnoptions exists
if not hasattr(sys, "warnoptions"): sys.warnoptions = []

async def test_real_balance():
    """Test real balance retrieval with fixed API"""
    try:
        print("[TEST] Testing REAL balance retrieval with fixed API...")
        
        # Import required components
        from bybit_bot.core.config import BotConfig
        from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
        
        # Initialize config and client
        config = BotConfig()
        client = EnhancedBybitClient(config)
        
        print("[TEST] Initializing client...")
        await client.initialize()
        
        print("[TEST] Retrieving account balance...")
        balance_data = await client.get_wallet_balance()
        
        if balance_data and 'list' in balance_data:
            print("[SUCCESS] Balance data retrieved!")
            
            for account in balance_data['list']:
                account_type = account.get('accountType', 'UNKNOWN')
                total_equity = float(account.get('totalEquity', 0))
                total_balance = float(account.get('totalWalletBalance', 0))
                available_balance = float(account.get('totalAvailableBalance', 0))
                
                print(f"\n[ACCOUNT] {account_type}")
                print(f"  Total Equity: ${total_equity:.4f}")
                print(f"  Total Balance: ${total_balance:.4f}")
                print(f"  Available: ${available_balance:.4f}")
                
                for coin in account.get('coin', []):
                    coin_name = coin.get('coin', 'UNKNOWN')
                    wallet_balance = float(coin.get('walletBalance', 0))
                    equity = float(coin.get('equity', 0))
                    
                    if wallet_balance > 0 or equity > 0:
                        print(f"    {coin_name}: Wallet=${wallet_balance:.6f}, Equity=${equity:.6f}")
            
            return True
        else:
            print(f"[ERROR] Invalid balance response: {balance_data}")
            return False
            
    except Exception as e:
        print(f"[ERROR] Balance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'client' in locals():
            await client.close()

async def test_market_data():
    """Test market data retrieval"""
    try:
        print("\n[TEST] Testing market data retrieval...")
        
        from bybit_bot.data_crawler.market_data_crawler import MarketDataCrawler
        from bybit_bot.core.config import BotConfig
        from bybit_bot.database.connection import DatabaseManager
        
        config = BotConfig()
        db_manager = DatabaseManager(config)
        
        crawler = MarketDataCrawler(config, db_manager)
        await crawler.initialize()
        
        print("[TEST] Testing BTCUSDT ticker...")
        # Test the fixed CCXT exchange
        ticker = await crawler.exchanges['bybit'].fetch_ticker('BTCUSDT')
        print(f"[SUCCESS] BTCUSDT: ${ticker['last']:,.2f}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Market data test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("=" * 60)
    print("TESTING API FIXES")
    print("=" * 60)
    
    success_count = 0
    
    # Test balance retrieval
    if await test_real_balance():
        success_count += 1
        print("\n[OK] Balance retrieval test PASSED")
    else:
        print("\n[FAIL] Balance retrieval test FAILED")
    
    # Test market data
    if await test_market_data():
        success_count += 1
        print("\n[OK] Market data test PASSED")
    else:
        print("\n[FAIL] Market data test FAILED")
    
    print("\n" + "=" * 60)
    print(f"API FIX TEST RESULTS: {success_count}/2 tests passed")
    print("=" * 60)
    
    return success_count == 2

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
