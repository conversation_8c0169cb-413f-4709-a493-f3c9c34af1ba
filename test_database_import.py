#!/usr/bin/env python3
"""
Test database_manager import specifically
"""
import sys
import traceback

# Ensure sys.warnoptions exists
if not hasattr(sys, "warnoptions"): 
    sys.warnoptions = []

print("[DEBUG] Testing database imports...")

try:
    print("[DEBUG] Testing config import...")
    from bybit_bot.core.config import BotConfig
    print("[OK] Config import successful")
    
    print("[DEBUG] Testing database_manager import...")
    from bybit_bot.database.connection import DatabaseManager
    print("[OK] DatabaseManager import successful")
    
except Exception as e:
    print(f"[ERROR] Import failed: {e}")
    print(f"[TRACEBACK] {traceback.format_exc()}")
    sys.exit(1)

print("[SUCCESS] Database import test completed successfully!")
