#!/usr/bin/env python3
"""
DIRECT BYBIT V5 API TEST
Simple test without complex classes
"""

import asyncio
import aiohttp

async def test_direct_api():
    """Direct test of Bybit V5 API"""
    
    # Test without authentication first
    url = "https://api.bybit.com/v5/market/tickers"
    
    # Test different categories
    test_cases = [
        {"category": "linear", "symbol": "BTCUSDT"},
        {"category": "spot", "symbol": "BTCUSDT"},
        {"category": "linear"},  # All linear symbols
        {"category": "spot"},    # All spot symbols
    ]
    
    async with aiohttp.ClientSession() as session:
        for i, params in enumerate(test_cases):
            try:
                print(f"\n[TEST {i+1}] Testing params: {params}")
                
                async with session.get(url, params=params) as response:
                    data = await response.json()
                    
                    print(f"Status: {response.status}")
                    print(f"RetCode: {data.get('retCode', 'N/A')}")
                    print(f"RetMsg: {data.get('retMsg', 'N/A')}")
                    
                    if data.get("retCode") == 0:
                        result_list = data.get("result", {}).get("list", [])
                        print(f"[OK] Success! Found {len(result_list)} items")
                        
                        if result_list and params.get("symbol"):
                            # Show price for specific symbol
                            ticker = result_list[0]
                            price = ticker.get("lastPrice", "N/A")
                            print(f"Price for {params['symbol']}: {price}")
                        elif result_list:
                            # Show first few symbols
                            symbols = [item.get("symbol", "N/A") for item in result_list[:3]]
                            print(f"First symbols: {symbols}")
                    else:
                        print(f"[ERROR] API Error: {data.get('retMsg', 'Unknown')}")
                        
            except Exception as e:
                print(f"[ERROR] Exception: {e}")

if __name__ == "__main__":
    asyncio.run(test_direct_api())
