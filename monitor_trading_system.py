#!/usr/bin/env python3
"""
TRADING SYSTEM STATUS MONITOR
Real-time monitoring of the trading system status and performance
"""

import time
import json
import sqlite3
import logging
from pathlib import Path
from datetime import datetime
import psutil

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TradingSystemMonitor:
    def __init__(self):
        self.db_path = "bybit_trading_bot.db"
        self.config_files = [
            "symbol_config.json",
            "validated_symbols.json", 
            "risk_config.json",
            "websocket_config.json",
            "database_config.json"
        ]
        
    def check_python_processes(self):
        """Check if trading system processes are running"""
        python_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] == 'python.exe' and proc.info['cmdline']:
                    cmdline = ' '.join(proc.info['cmdline'])
                    if 'main_unified_system' in cmdline:
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'cmdline': cmdline,
                            'status': 'running'
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        return python_processes
    
    def check_database_status(self):
        """Check database connectivity and basic stats"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            # Get some basic stats
            stats = {}
            if ('trades',) in tables:
                cursor.execute("SELECT COUNT(*) FROM trades")
                stats['total_trades'] = cursor.fetchone()[0]
                
            if ('positions',) in tables:
                cursor.execute("SELECT COUNT(*) FROM positions WHERE status = 'open'")
                stats['open_positions'] = cursor.fetchone()[0]
                
            conn.close()
            
            return {
                'status': 'connected',
                'tables': len(tables),
                'stats': stats
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def check_config_files(self):
        """Check if all config files exist and are valid"""
        config_status = {}
        
        for config_file in self.config_files:
            path = Path(config_file)
            if path.exists():
                try:
                    with open(path, 'r') as f:
                        data = json.load(f)
                    config_status[config_file] = {
                        'status': 'ok',
                        'size': len(str(data))
                    }
                except Exception as e:
                    config_status[config_file] = {
                        'status': 'error',
                        'error': str(e)
                    }
            else:
                config_status[config_file] = {
                    'status': 'missing'
                }
                
        return config_status
    
    def check_system_resources(self):
        """Check system resource usage"""
        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('.').percent
        }
    
    def generate_status_report(self):
        """Generate comprehensive status report"""
        logger.info("=== TRADING SYSTEM STATUS REPORT ===")
        
        # Check processes
        processes = self.check_python_processes()
        logger.info(f"[PROCESSES] Trading system processes: {len(processes)}")
        for proc in processes:
            logger.info(f"  - PID {proc['pid']}: {proc['status']}")
        
        # Check database
        db_status = self.check_database_status()
        logger.info(f"[DATABASE] Status: {db_status['status']}")
        if db_status['status'] == 'connected':
            logger.info(f"  - Tables: {db_status['tables']}")
            if 'stats' in db_status:
                for key, value in db_status['stats'].items():
                    logger.info(f"  - {key}: {value}")
        
        # Check config files
        configs = self.check_config_files()
        logger.info(f"[CONFIGS] Configuration files:")
        for config, status in configs.items():
            logger.info(f"  - {config}: {status['status']}")
        
        # Check resources
        resources = self.check_system_resources()
        logger.info(f"[RESOURCES] System usage:")
        logger.info(f"  - CPU: {resources['cpu_percent']:.1f}%")
        logger.info(f"  - Memory: {resources['memory_percent']:.1f}%")
        logger.info(f"  - Disk: {resources['disk_percent']:.1f}%")
        
        # Overall status
        overall_status = "OPERATIONAL" if len(processes) > 0 and db_status['status'] == 'connected' else "ISSUES_DETECTED"
        logger.info(f"[OVERALL] System status: {overall_status}")
        
        return {
            'timestamp': datetime.now().isoformat(),
            'processes': processes,
            'database': db_status,
            'configs': configs,
            'resources': resources,
            'overall_status': overall_status
        }

def main():
    """Main monitoring function"""
    monitor = TradingSystemMonitor()
    
    logger.info("Starting trading system status monitoring...")
    
    try:
        while True:
            status = monitor.generate_status_report()
            
            # Save status to file
            with open('system_status.json', 'w') as f:
                json.dump(status, f, indent=2)
            
            print(f"\n[STATUS] {status['overall_status']} - Report saved to system_status.json")
            print(f"[INFO] Processes: {len(status['processes'])}, DB: {status['database']['status']}")
            
            # Wait 30 seconds before next check
            time.sleep(30)
            
    except KeyboardInterrupt:
        logger.info("Monitoring stopped by user")

if __name__ == "__main__":
    main()
