#!/usr/bin/env python3
"""
API AUTHENTICATION AND SYMBOL FIX
Addresses the specific retCode 10001 'invalid symbol' errors
"""

import asyncio
import aiohttp
import json
import time
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_bybit_api_direct():
    """Test Bybit API directly with correct parameters"""
    
    # Test public endpoint first (no auth required)
    public_url = "https://api.bybit.com/v5/market/tickers"
    params = {
        "category": "linear",
        "symbol": "BTCUSDT"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(public_url, params=params) as response:
                result = await response.json()
                
                if result.get("retCode") == 0:
                    logger.info(f"[OK] Public API test successful: {result['result']['list'][0]['symbol']}")
                    logger.info(f"[OK] BTC Price: ${float(result['result']['list'][0]['lastPrice']):.2f}")
                    return True
                else:
                    logger.error(f"[ERROR] Public API failed: {result}")
                    return False
                    
    except Exception as e:
        logger.error(f"[ERROR] API test failed: {e}")
        return False

async def test_kline_endpoint():
    """Test the specific kline endpoint that's failing"""
    
    kline_url = "https://api.bybit.com/v5/market/kline"
    params = {
        "category": "linear",
        "symbol": "BTCUSDT",
        "interval": "1",
        "limit": 100
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(kline_url, params=params) as response:
                result = await response.json()
                
                if result.get("retCode") == 0:
                    klines = result['result']['list']
                    logger.info(f"[OK] Kline test successful: {len(klines)} candles retrieved")
                    logger.info(f"[OK] Latest close: ${float(klines[0][4]):.2f}")
                    return True
                else:
                    logger.error(f"[ERROR] Kline API failed: {result}")
                    return False
                    
    except Exception as e:
        logger.error(f"[ERROR] Kline test failed: {e}")
        return False

async def validate_all_symbols():
    """Validate all trading symbols"""
    
    symbols = [
        "BTCUSDT", "ETHUSDT", "SOLUSDT", "ADAUSDT", "DOTUSDT",
        "LINKUSDT", "AVAXUSDT", "MATICUSDT", "ATOMUSDT", "NEARUSDT"
    ]
    
    valid_symbols = []
    
    for symbol in symbols:
        url = "https://api.bybit.com/v5/market/tickers"
        params = {"category": "linear", "symbol": symbol}
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    result = await response.json()
                    
                    if result.get("retCode") == 0 and result.get("result", {}).get("list"):
                        valid_symbols.append(symbol)
                        price = float(result["result"]["list"][0]["lastPrice"])
                        logger.info(f"[OK] {symbol}: ${price:.4f}")
                    else:
                        logger.warning(f"[INVALID] {symbol}: {result.get('retMsg', 'Unknown error')}")
                        
        except Exception as e:
            logger.error(f"[ERROR] {symbol} validation failed: {e}")
            
        # Small delay to avoid rate limiting
        await asyncio.sleep(0.1)
    
    logger.info(f"[RESULT] Valid symbols: {len(valid_symbols)}/{len(symbols)}")
    return valid_symbols

async def main():
    """Run all API validation tests"""
    logger.info("=== BYBIT API VALIDATION AND FIX ===")
    
    # Test 1: Public API
    logger.info("[TEST 1] Testing public API...")
    if await test_bybit_api_direct():
        logger.info("[OK] Public API working")
    else:
        logger.error("[FAIL] Public API failed")
        return
    
    # Test 2: Kline endpoint
    logger.info("[TEST 2] Testing kline endpoint...")
    if await test_kline_endpoint():
        logger.info("[OK] Kline endpoint working")
    else:
        logger.error("[FAIL] Kline endpoint failed")
        return
        
    # Test 3: Symbol validation
    logger.info("[TEST 3] Validating all symbols...")
    valid_symbols = await validate_all_symbols()
    
    if len(valid_symbols) >= 5:
        logger.info("[SUCCESS] API validation completed successfully!")
        
        # Save validated symbols
        with open('validated_symbols.json', 'w') as f:
            json.dump({
                'valid_symbols': valid_symbols,
                'test_time': time.time(),
                'api_status': 'operational'
            }, f, indent=2)
            
        print(f"[OK] {len(valid_symbols)} symbols validated and saved")
    else:
        logger.error("[FAILURE] Too few valid symbols found")
        print("[ERROR] API validation failed")

if __name__ == "__main__":
    asyncio.run(main())
