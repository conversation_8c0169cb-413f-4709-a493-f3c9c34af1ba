#!/usr/bin/env python3
"""
SYSTEMATIC DEBUGGING OF main_unified_system.py IMPORTS
Find the exact import that's causing the hang/failure
"""
import sys
import traceback
import time

# Ensure sys.warnoptions exists
if not hasattr(sys, "warnoptions"): 
    sys.warnoptions = []

def test_import(module_path, description):
    """Test a specific import and report results"""
    print(f"[TEST] {description}...")
    start_time = time.time()
    try:
        exec(f"import {module_path}")
        elapsed = time.time() - start_time
        print(f"[OK] {description} - {elapsed:.2f}s")
        return True
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"[ERROR] {description} failed after {elapsed:.2f}s: {e}")
        print(f"[TRACEBACK] {traceback.format_exc()}")
        return False

def test_from_import(from_module, import_items, description):
    """Test a specific from...import and report results"""
    print(f"[TEST] {description}...")
    start_time = time.time()
    try:
        exec(f"from {from_module} import {import_items}")
        elapsed = time.time() - start_time
        print(f"[OK] {description} - {elapsed:.2f}s")
        return True
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"[ERROR] {description} failed after {elapsed:.2f}s: {e}")
        print(f"[TRACEBACK] {traceback.format_exc()}")
        return False

print("=" * 80)
print("SYSTEMATIC IMPORT DEBUGGING FOR main_unified_system.py")
print("=" * 80)

# Test basic imports first
basic_imports = [
    ("sys", "sys module"),
    ("os", "os module"),
    ("warnings", "warnings module"),
    ("logging", "logging module"),
    ("signal", "signal module"),
    ("types", "types module"),
    ("importlib", "importlib module"),
    ("json", "json module"),
    ("asyncio", "asyncio module"),
    ("datetime", "datetime module"),
    ("typing", "typing module"),
    ("pathlib", "pathlib module"),
]

print("\n[PHASE 1] Testing basic Python imports...")
for module, desc in basic_imports:
    if not test_import(module, desc):
        sys.exit(1)

# Test third-party imports
print("\n[PHASE 2] Testing third-party imports...")
third_party_imports = [
    ("uvicorn", "uvicorn server"),
    ("fastapi", "FastAPI framework"),
]

for module, desc in third_party_imports:
    if not test_import(module, desc):
        sys.exit(1)

# Test FastAPI specific imports
print("\n[PHASE 3] Testing FastAPI specific imports...")
fastapi_imports = [
    ("fastapi", "FastAPI", "FastAPI main class"),
    ("fastapi.middleware.cors", "CORSMiddleware", "CORS middleware"),
    ("fastapi.responses", "HTMLResponse", "HTML response"),
    ("fastapi.staticfiles", "StaticFiles", "Static files"),
]

for from_mod, import_items, desc in fastapi_imports:
    if not test_from_import(from_mod, import_items, desc):
        sys.exit(1)

# Test core bot imports
print("\n[PHASE 4] Testing core bot imports...")
core_imports = [
    ("bybit_bot.core.config", "BotConfig", "Bot configuration"),
    ("bybit_bot.database.connection", "DatabaseManager", "Database manager"),
    ("bybit_bot.core.logger", "setup_logging, TradingBotLogger", "Logging system"),
    ("bybit_bot.monitoring.hardware_monitor", "HardwareMonitor", "Hardware monitor"),
]

for from_mod, import_items, desc in core_imports:
    if not test_from_import(from_mod, import_items, desc):
        print(f"[CRITICAL] Core import failed: {desc}")
        sys.exit(1)

# Test problematic imports one by one
print("\n[PHASE 5] Testing potentially problematic imports...")
problematic_imports = [
    ("bybit_bot.core.bot_manager", "BotManager", "Bot manager"),
    ("bybit_bot.exchange.enhanced_bybit_client", "EnhancedBybitClient", "Enhanced Bybit client"),
    ("bybit_bot.profit_maximization.advanced_profit_engine", "AdvancedProfitEngine", "Advanced profit engine"),
    ("bybit_bot.profit_maximization.hyper_profit_engine", "HyperProfitEngine", "Hyper profit engine"),
]

for from_mod, import_items, desc in problematic_imports:
    if not test_from_import(from_mod, import_items, desc):
        print(f"[IDENTIFIED] Problem import: {desc} from {from_mod}")
        # Don't exit - continue to test others

print("\n[SUCCESS] Import debugging completed!")
print("Check the output above to identify which imports are failing or hanging.")
