#!/usr/bin/env python3
"""
BYBIT TRADING ACTIVITY DIAGNOSTIC
Diagnoses why the trading system isn't executing actual trades on Bybit account
"""

import asyncio
import aiohttp
import sqlite3
import json
import hmac
import hashlib
import time
from urllib.parse import urlencode
from datetime import datetime
from pathlib import Path
import sys
import os

# Add the bybit_bot directory to Python path
sys.path.append(str(Path(__file__).parent / "bybit_bot"))

class BybitTradingDiagnostic:
    def __init__(self):
        self.base_url = "https://api.bybit.com"
        self.api_key = None
        self.api_secret = None
        self.db_path = "bybit_trading_bot.db"
        
    def load_credentials(self):
        """Load API credentials from environment or config"""
        try:
            # Try loading from encrypted credentials
            if Path('credentials.salt').exists():
                print("[INFO] Found encrypted credentials file")
                return True
            
            # Try loading from environment
            self.api_key = os.getenv('BYBIT_API_KEY')
            self.api_secret = os.getenv('BYBIT_API_SECRET')
            
            if self.api_key and self.api_secret:
                print(f"[INFO] Loaded credentials from environment: {self.api_key[:10]}...")
                return True
            else:
                print("[WARNING] No API credentials found in environment")
                return False
                
        except Exception as e:
            print(f"[ERROR] Failed to load credentials: {e}")
            return False
    
    def generate_signature(self, params: dict, timestamp: str) -> str:
        """Generate Bybit API signature"""
        if not self.api_secret:
            return ""
            
        query_string = urlencode(sorted(params.items()))
        param_str = f"{timestamp}{self.api_key}{query_string}"
        
        return hmac.new(
            self.api_secret.encode('utf-8'),
            param_str.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    async def check_account_balance(self):
        """Check actual account balance on Bybit"""
        try:
            if not self.api_key or not self.api_secret:
                return {"error": "No API credentials"}
            
            timestamp = str(int(time.time() * 1000))
            params = {
                "accountType": "UNIFIED"
            }
            
            signature = self.generate_signature(params, timestamp)
            
            headers = {
                "X-BAPI-API-KEY": self.api_key,
                "X-BAPI-SIGN": signature,
                "X-BAPI-SIGN-TYPE": "2",
                "X-BAPI-TIMESTAMP": timestamp,
                "X-BAPI-RECV-WINDOW": "5000"
            }
            
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/v5/account/wallet-balance"
                async with session.get(url, params=params, headers=headers) as response:
                    result = await response.json()
                    
                    if result.get("retCode") == 0:
                        wallet = result.get("result", {}).get("list", [])
                        if wallet:
                            coins = wallet[0].get("coin", [])
                            return {"success": True, "coins": coins}
                    
                    return {"error": result.get("retMsg", "Unknown error"), "code": result.get("retCode")}
                    
        except Exception as e:
            return {"error": str(e)}
    
    async def check_open_orders(self):
        """Check for any open orders"""
        try:
            if not self.api_key or not self.api_secret:
                return {"error": "No API credentials"}
            
            timestamp = str(int(time.time() * 1000))
            params = {
                "category": "linear",
                "openOnly": "0"
            }
            
            signature = self.generate_signature(params, timestamp)
            
            headers = {
                "X-BAPI-API-KEY": self.api_key,
                "X-BAPI-SIGN": signature,
                "X-BAPI-SIGN-TYPE": "2",
                "X-BAPI-TIMESTAMP": timestamp,
                "X-BAPI-RECV-WINDOW": "5000"
            }
            
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/v5/order/realtime"
                async with session.get(url, params=params, headers=headers) as response:
                    result = await response.json()
                    
                    if result.get("retCode") == 0:
                        orders = result.get("result", {}).get("list", [])
                        return {"success": True, "orders": orders}
                    
                    return {"error": result.get("retMsg", "Unknown error"), "code": result.get("retCode")}
                    
        except Exception as e:
            return {"error": str(e)}
    
    async def check_trading_history(self):
        """Check recent trading history"""
        try:
            if not self.api_key or not self.api_secret:
                return {"error": "No API credentials"}
            
            timestamp = str(int(time.time() * 1000))
            params = {
                "category": "linear",
                "limit": "50"
            }
            
            signature = self.generate_signature(params, timestamp)
            
            headers = {
                "X-BAPI-API-KEY": self.api_key,
                "X-BAPI-SIGN": signature,
                "X-BAPI-SIGN-TYPE": "2",
                "X-BAPI-TIMESTAMP": timestamp,
                "X-BAPI-RECV-WINDOW": "5000"
            }
            
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/v5/execution/list"
                async with session.get(url, params=params, headers=headers) as response:
                    result = await response.json()
                    
                    if result.get("retCode") == 0:
                        executions = result.get("result", {}).get("list", [])
                        return {"success": True, "executions": executions}
                    
                    return {"error": result.get("retMsg", "Unknown error"), "code": result.get("retCode")}
                    
        except Exception as e:
            return {"error": str(e)}
    
    def check_database_activity(self):
        """Check database for trading activity"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check trades table
            cursor.execute("SELECT COUNT(*) FROM trades")
            total_trades = cursor.fetchone()[0]
            
            # Check recent trades (last 24 hours)
            cursor.execute("""
                SELECT COUNT(*) FROM trades 
                WHERE datetime(timestamp) > datetime('now', '-1 day')
            """)
            recent_trades = cursor.fetchone()[0]
            
            # Check orders table if exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='orders'")
            has_orders = cursor.fetchone() is not None
            
            orders_count = 0
            if has_orders:
                cursor.execute("SELECT COUNT(*) FROM orders")
                orders_count = cursor.fetchone()[0]
            
            # Check system logs
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='system_logs'")
            has_logs = cursor.fetchone() is not None
            
            recent_logs = []
            if has_logs:
                cursor.execute("""
                    SELECT timestamp, level, message FROM system_logs 
                    WHERE datetime(timestamp) > datetime('now', '-1 hour')
                    ORDER BY timestamp DESC LIMIT 10
                """)
                recent_logs = cursor.fetchall()
            
            conn.close()
            
            return {
                "total_trades": total_trades,
                "recent_trades": recent_trades,
                "total_orders": orders_count,
                "recent_logs": recent_logs,
                "has_activity": total_trades > 0 or orders_count > 0
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def check_trading_config(self):
        """Check trading configuration for issues"""
        issues = []
        
        # Check if paper trading is enabled
        config_files = ['config.yaml', 'config_template.yaml', 'config_secure_template.yaml']
        
        for config_file in config_files:
            if Path(config_file).exists():
                try:
                    with open(config_file, 'r') as f:
                        content = f.read()
                        if 'paper_trading: true' in content or 'paper_trading: True' in content:
                            issues.append(f"Paper trading enabled in {config_file}")
                        if 'live_trading: false' in content or 'live_trading: False' in content:
                            issues.append(f"Live trading disabled in {config_file}")
                except:
                    pass
        
        # Check risk configuration
        if Path('risk_config.json').exists():
            try:
                with open('risk_config.json', 'r') as f:
                    risk_config = json.load(f)
                    
                if risk_config.get('emergency_stop', False):
                    issues.append("Emergency stop is enabled")
                    
                max_position = risk_config.get('max_position_size', 0)
                if max_position <= 0:
                    issues.append("Max position size is 0 or negative")
                    
                account_balance = risk_config.get('account_balance', 0)
                if account_balance <= 0:
                    issues.append("Account balance is 0 or not set")
                    
            except:
                issues.append("Risk config file is corrupted")
        
        return issues

async def main():
    """Main diagnostic function"""
    print("=== BYBIT TRADING ACTIVITY DIAGNOSTIC ===")
    print("Checking why no trades are executing on your Bybit account...\n")
    
    diagnostic = BybitTradingDiagnostic()
    
    # 1. Check credentials
    print("[1] CHECKING API CREDENTIALS...")
    has_credentials = diagnostic.load_credentials()
    if not has_credentials:
        print("❌ No API credentials found - this prevents live trading")
        print("   - Check environment variables BYBIT_API_KEY and BYBIT_API_SECRET")
        print("   - Check if credentials.salt file exists and is readable")
    else:
        print("✅ API credentials loaded")
    
    # 2. Check account balance
    print("\n[2] CHECKING BYBIT ACCOUNT BALANCE...")
    balance_result = await diagnostic.check_account_balance()
    if "error" in balance_result:
        print(f"❌ Failed to get account balance: {balance_result['error']}")
        if balance_result.get('code') == 10003:
            print("   - API key is invalid or expired")
        elif balance_result.get('code') == 10004:
            print("   - API signature verification failed")
    else:
        coins = balance_result.get('coins', [])
        total_balance = 0
        for coin in coins:
            if coin.get('coin') == 'USDT':
                total_balance = float(coin.get('walletBalance', 0))
                break
        print(f"✅ Account balance: ${total_balance:.2f} USDT")
        if total_balance <= 0:
            print("❌ Insufficient balance for trading")
    
    # 3. Check open orders
    print("\n[3] CHECKING OPEN ORDERS...")
    orders_result = await diagnostic.check_open_orders()
    if "error" in orders_result:
        print(f"❌ Failed to get orders: {orders_result['error']}")
    else:
        orders = orders_result.get('orders', [])
        print(f"📋 Open orders: {len(orders)}")
        for order in orders[:3]:  # Show first 3 orders
            print(f"   - {order.get('symbol')} {order.get('side')} {order.get('qty')} @ {order.get('price')}")
    
    # 4. Check trading history
    print("\n[4] CHECKING TRADING HISTORY...")
    history_result = await diagnostic.check_trading_history()
    if "error" in history_result:
        print(f"❌ Failed to get trading history: {history_result['error']}")
    else:
        executions = history_result.get('executions', [])
        print(f"📊 Recent executions: {len(executions)}")
        if len(executions) == 0:
            print("❌ No trading activity found on Bybit account")
        else:
            for execution in executions[:3]:  # Show first 3 executions
                print(f"   - {execution.get('symbol')} {execution.get('side')} {execution.get('execQty')} @ {execution.get('execPrice')}")
    
    # 5. Check database activity
    print("\n[5] CHECKING DATABASE ACTIVITY...")
    db_result = diagnostic.check_database_activity()
    if "error" in db_result:
        print(f"❌ Database check failed: {db_result['error']}")
    else:
        print(f"📊 Total trades in database: {db_result['total_trades']}")
        print(f"📊 Recent trades (24h): {db_result['recent_trades']}")
        print(f"📊 Total orders in database: {db_result['total_orders']}")
        
        if not db_result['has_activity']:
            print("❌ No trading activity recorded in database")
    
    # 6. Check configuration issues
    print("\n[6] CHECKING CONFIGURATION ISSUES...")
    config_issues = diagnostic.check_trading_config()
    if config_issues:
        print("❌ Configuration issues found:")
        for issue in config_issues:
            print(f"   - {issue}")
    else:
        print("✅ No obvious configuration issues found")
    
    # Summary and recommendations
    print("\n" + "="*50)
    print("DIAGNOSTIC SUMMARY")
    print("="*50)
    
    if not has_credentials:
        print("🔴 CRITICAL: No API credentials - Set up valid Bybit API keys")
    elif balance_result.get('error'):
        print("🔴 CRITICAL: API connection failed - Check API key permissions")
    elif total_balance <= 0:
        print("🔴 CRITICAL: Insufficient account balance for trading")
    elif not db_result.get('has_activity') and len(executions) == 0:
        print("🟡 WARNING: System running but no trades executing")
        print("\nPOSSIBLE CAUSES:")
        print("1. Trading conditions not met (price, volatility, signals)")
        print("2. Risk management preventing trades (position limits)")
        print("3. Paper trading mode enabled instead of live trading")
        print("4. Strategy parameters too conservative")
        print("5. Market conditions unfavorable for configured strategies")
    else:
        print("🟢 Trading system appears to be functioning normally")
    
    # Save diagnostic report
    report = {
        "timestamp": datetime.now().isoformat(),
        "credentials": has_credentials,
        "balance": balance_result,
        "orders": orders_result,
        "history": history_result,
        "database": db_result,
        "config_issues": config_issues
    }
    
    with open('trading_diagnostic_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Full diagnostic report saved to: trading_diagnostic_report.json")

if __name__ == "__main__":
    asyncio.run(main())
