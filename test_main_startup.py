#!/usr/bin/env python3
"""
Test main_unified_system.py startup and configuration
"""
import sys
import traceback

# Ensure sys.warnoptions exists
if not hasattr(sys, "warnoptions"): 
    sys.warnoptions = []

print("[TEST] Testing main unified system startup...")

try:
    print("[TEST] Step 1: Testing BotConfig import...")
    from bybit_bot.core.config import BotConfig
    print("[OK] BotConfig imported successfully")
    
    print("[TEST] Step 2: Creating config instance...")
    config = BotConfig()
    print(f"[OK] Config created - api_host: {config.api_host}, api_port: {config.api_port}")
    
    print("[TEST] Step 3: Testing main_unified_system import...")
    import main_unified_system
    print("[OK] Main unified system imported successfully")
    
    print("[TEST] Step 4: Testing uvicorn import...")
    import uvicorn
    print("[OK] Uvicorn imported successfully")
    
    print("[TEST] Step 5: Testing FastAPI app access...")
    app = main_unified_system.app
    print(f"[OK] FastAPI app accessible: {type(app)}")
    
    print("[TEST] Step 6: Testing main function...")
    # Don't actually call main() as it will start the server
    print("[OK] All imports successful - main function should work")
    
    print("[SUCCESS] Main system startup test completed successfully!")
    print(f"[INFO] The system should start on http://{config.api_host}:{config.api_port}")
    
except Exception as e:
    print(f"[ERROR] Main system startup test failed: {e}")
    print(f"[TRACEBACK] {traceback.format_exc()}")
