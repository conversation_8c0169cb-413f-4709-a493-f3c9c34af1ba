#!/usr/bin/env python3
"""
Debug strategy manager imports specifically
"""
import sys
import traceback

# Ensure sys.warnoptions exists
if not hasattr(sys, "warnoptions"): 
    sys.warnoptions = []

print("[DEBUG] Testing strategy manager imports specifically...")

try:
    print("[DEBUG] Testing basic imports...")
    from bybit_bot.core.config import BotConfig
    print("[OK] BotConfig import successful")
    
    print("[DEBUG] Testing strategy manager import directly...")
    from bybit_bot.strategies.strategy_manager import StrategyManager
    print("[OK] StrategyManager import successful")
    
except Exception as e:
    print(f"[ERROR] Import failed: {e}")
    print(f"[TRACEBACK] {traceback.format_exc()}")
    sys.exit(1)

print("[SUCCESS] Strategy manager imports test completed successfully!")
