#!/usr/bin/env python3
"""
SIMPLE API TEST
Quick test of the API fixes
"""

import asyncio

async def test_market_data():
    """Quick test of market data crawler"""
    try:
        print("[TEST] Testing market data crawler import...")
        from bybit_bot.data_crawler.market_data_crawler import MarketDataCrawler
        print("[SUCCESS] Market data crawler imported successfully!")
        
        print("[TEST] Testing CCXT import...")
        import ccxt.async_support as ccxt
        print("[SUCCESS] CCXT imported successfully!")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_market_data())
    print(f"[RESULT] {'PASSED' if success else 'FAILED'}")
