#!/usr/bin/env python3
"""
Debug main_unified_system.py imports step by step
"""
import sys
import traceback

# Ensure sys.warnoptions exists
if not hasattr(sys, "warnoptions"): 
    sys.warnoptions = []

print("[DEBUG] Testing main_unified_system imports step by step...")

try:
    print("[DEBUG] Testing basic imports...")
    import asyncio
    import signal
    import logging
    from datetime import datetime
    from typing import Any, Optional, Type, List, Union, Dict, Callable, Awaitable
    from pathlib import Path
    print("[OK] Basic imports successful")
    
    print("[DEBUG] Testing uvicorn and FastAPI...")
    import uvicorn
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import HTMLResponse
    print("[OK] FastAPI imports successful")
    
    print("[DEBUG] Testing core bot imports...")
    from bybit_bot.core.config import BotConfig
    print("[OK] BotConfig import successful")
    
    from bybit_bot.database.connection import DatabaseManager
    print("[OK] DatabaseManager import successful")
    
    print("[DEBUG] Testing bot manager import...")
    from bybit_bot.core.bot_manager import BotManager
    print("[OK] Bot manager import successful")
    
    print("[DEBUG] Testing logger import...")
    from bybit_bot.core.logger import setup_logging, TradingBotLogger
    print("[OK] Logger imports successful")
    
    print("[DEBUG] Testing hardware monitor import...")
    from bybit_bot.monitoring.hardware_monitor import HardwareMonitor
    print("[OK] Hardware monitor import successful")
    
    print("[DEBUG] Testing enhanced client import...")
    from bybit_bot.exchange.enhanced_bybit_client import EnhancedBybitClient
    print("[OK] Enhanced client import successful")
    
    print("[DEBUG] All core imports successful - checking profit engines...")
    
except Exception as e:
    print(f"[ERROR] Import failed at step: {e}")
    print(f"[TRACEBACK] {traceback.format_exc()}")
    sys.exit(1)

print("[SUCCESS] Core imports test completed successfully!")
