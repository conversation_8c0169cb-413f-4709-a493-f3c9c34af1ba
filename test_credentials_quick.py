#!/usr/bin/env python3
"""Quick credential test to verify encrypted credential loading"""

import sys

# Add current directory to path
sys.path.append('.')

def test_credentials():
    """Test credential loading"""
    try:
        from bybit_bot.security.credential_manager import get_secure_credential
        
        print("[TEST] Loading encrypted credentials...")
        
        api_key = get_secure_credential('BYBIT_API_KEY')
        api_secret = get_secure_credential('BYBIT_API_SECRET')
        
        print(f"[TEST] API Key status: {'LOADED' if api_key else 'MISSING'} (length: {len(api_key) if api_key else 0})")
        print(f"[TEST] API Secret status: {'LOADED' if api_secret else 'MISSING'} (length: {len(api_secret) if api_secret else 0})")
        
        if api_key and api_secret and len(api_key) > 10 and len(api_secret) > 10:
            print(f"[TEST] API Key: {api_key[:6]}...{api_key[-4:]}")
            print(f"[TEST] API Secret: {api_secret[:4]}...{api_secret[-4:]}")
            print("[OK] Valid credentials loaded successfully")
            return True
        else:
            print("[ERROR] Failed to load valid credentials")
            return False
            
    except Exception as e:
        print(f"[ERROR] Credential test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_credentials()
    if success:
        print("\n[CREDENTIAL TEST PASSED] Ready for live trading")
    else:
        print("\n[CREDENTIAL TEST FAILED] Cannot proceed with live trading")
