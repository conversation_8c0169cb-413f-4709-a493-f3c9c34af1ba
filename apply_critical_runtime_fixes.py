#!/usr/bin/env python3
"""
Critical Runtime Fixes for Bybit Trading Bot
Applies essential fixes for sys.warnoptions and other runtime issues.
"""

def apply_comprehensive_warnoptions_fix():
    """Apply comprehensive sys.warnoptions fix with fallback detection."""
    print("[STARTING] Comprehensive system-wide warning suppression...")
    
    try:
        # First ensure sys.warnoptions exists with proper initialization
        import sys
        import warnings
        
        # Create sys.warnoptions if missing or reinitialize if corrupted
        if not hasattr(sys, 'warnoptions') or sys.warnoptions is None:
            sys.warnoptions = []
            print("[FIXED] Created missing sys.warnoptions attribute")
        elif not isinstance(sys.warnoptions, list):
            # Fix corrupted warnoptions
            sys.warnoptions = []
            print("[FIXED] Repaired corrupted sys.warnoptions")
        
        # Also ensure access to warnoptions works
        try:
            _ = len(sys.warnoptions)
        except (AttributeError, TypeError):
            sys.warnoptions = []
            print("[FIXED] Restored sys.warnoptions access")
        
        # Suppress warnings at all levels
        warnings.filterwarnings("ignore")
        
        # Set environment variable to suppress warnings
        import os
        os.environ['PYTHONWARNINGS'] = 'ignore'
        
        # Pandas warnings
        try:
            import pandas as pd
            pd.options.mode.chained_assignment = None
            print("[FIXED] Pandas warnings suppressed")
        except ImportError:
            pass
        
        # Numpy warnings  
        try:
            import numpy as np
            np.seterr(all='ignore')
            print("[FIXED] Numpy warnings suppressed")
        except ImportError:
            pass
        
        # Sklearn warnings
        try:
            import sklearn
            sklearn.set_config(assume_finite=True)
            print("[FIXED] Sklearn warnings suppressed")
        except ImportError:
            pass
        
        # Asyncio/websocket warnings
        import asyncio
        import logging
        logging.getLogger('asyncio').setLevel(logging.ERROR)
        logging.getLogger('websockets').setLevel(logging.ERROR)
        print("[FIXED] Asyncio/websocket warnings suppressed")
        
        # Database/HTTP warnings
        logging.getLogger('aiohttp').setLevel(logging.ERROR)
        logging.getLogger('aiosqlite').setLevel(logging.ERROR)
        print("[FIXED] Database/HTTP warnings suppressed")
        
        print("[SUCCESS] COMPREHENSIVE sys.warnoptions fix applied - ALL LAYERS ACTIVE")
        return True
        
    except Exception as e:
        print(f"[ERROR] Failed to apply warnoptions fix: {e}")
        # Emergency fallback
        import sys
        sys.warnoptions = []
        return False

if __name__ == "__main__":
    apply_comprehensive_warnoptions_fix()
