#!/usr/bin/env python3
"""
BYBIT V5 API CATEGORY TEST
Test different category parameters to find the correct ones
"""

import asyncio
import logging
import aiohttp
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_manager import ConfigManager

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_bybit_categories():
    """Test different Bybit V5 API categories"""
    
    # Initialize credentials using ConfigManager
    config_manager = ConfigManager()
    
    # Get exchange config
    exchange_config = config_manager.get_exchange_config()
    
    api_key = exchange_config.api_key
    api_secret = exchange_config.secret_key
    
    if not api_key or not api_secret:
        logger.error("Failed to load credentials")
        return
    
    logger.info(f"[OK] Credentials loaded: {api_key[:8]}...{api_key[-4:]}")
    
    # Test categories
    categories = ["linear", "spot", "inverse", "option"]
    symbol = "BTCUSDT"
    
    async with aiohttp.ClientSession() as session:
        for category in categories:
            try:
                url = "https://api.bybit.com/v5/market/tickers"
                params = {
                    "category": category,
                    "symbol": symbol
                }
                
                async with session.get(url, params=params) as response:
                    data = await response.json()
                    
                    if data.get("retCode") == 0:
                        logger.info(f"[OK] Category '{category}' works for {symbol}")
                        if data["result"]["list"]:
                            ticker = data["result"]["list"][0]
                            price = ticker["lastPrice"]
                            logger.info(f"    Price: {price}")
                    else:
                        logger.warning(f"[ERROR] Category '{category}' failed: {data.get('retMsg', 'Unknown error')}")
                        
            except Exception as e:
                logger.error(f"[ERROR] Category '{category}' exception: {e}")
    
    # Also test without symbol to see all available symbols in each category
    logger.info("\n[INFO] Testing categories without symbol filter...")
    async with aiohttp.ClientSession() as session:
        for category in categories:
            try:
                url = "https://api.bybit.com/v5/market/tickers"
                params = {"category": category}
                
                async with session.get(url, params=params) as response:
                    data = await response.json()
                    
                    if data.get("retCode") == 0:
                        count = len(data["result"]["list"])
                        logger.info(f"[OK] Category '{category}' has {count} symbols")
                        
                        # Show first few symbols
                        if data["result"]["list"]:
                            symbols = [item["symbol"] for item in data["result"]["list"][:5]]
                            logger.info(f"    First symbols: {symbols}")
                    else:
                        logger.warning(f"[ERROR] Category '{category}' failed: {data.get('retMsg', 'Unknown error')}")
                        
            except Exception as e:
                logger.error(f"[ERROR] Category '{category}' exception: {e}")

if __name__ == "__main__":
    asyncio.run(test_bybit_categories())
