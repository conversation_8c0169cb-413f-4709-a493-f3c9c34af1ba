#!/usr/bin/env python3
"""
Test bot_manager import specifically
"""
import sys
import traceback

# Ensure sys.warnoptions exists
if not hasattr(sys, "warnoptions"): 
    sys.warnoptions = []

print("[DEBUG] Testing bot_manager import specifically...")

try:
    print("[DEBUG] Pre-loading all dependencies...")
    from bybit_bot.core.config import BotConfig
    from bybit_bot.core.logger import TradingBotLogger
    from bybit_bot.database.connection import DatabaseManager
    from bybit_bot.exchange.bybit_client import BybitClient
    from bybit_bot.monitoring.hardware_monitor import HardwareMonitor
    print("[OK] Dependencies pre-loaded")
    
    print("[DEBUG] Testing bot_manager import...")
    from bybit_bot.core.bot_manager import BotManager, BotState
    print("[OK] BotManager import successful")
    
except Exception as e:
    print(f"[ERROR] Import failed: {e}")
    print(f"[TRACEBACK] {traceback.format_exc()}")
    sys.exit(1)

print("[SUCCESS] Bot manager import test completed successfully!")
