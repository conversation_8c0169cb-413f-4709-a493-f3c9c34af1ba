#!/usr/bin/env python3
"""
Test individual imports to find the hanging one
"""
import sys
import traceback

# Ensure sys.warnoptions exists
if not hasattr(sys, "warnoptions"): 
    sys.warnoptions = []

print("[DEBUG] Testing individual imports...")

try:
    print("[DEBUG] Testing basic imports...")
    import asyncio
    import time
    import logging
    print("[OK] Basic imports successful")
    
    print("[DEBUG] Testing config import...")
    from bybit_bot.core.config import BotConfig
    print("[OK] Config import successful")
    
    print("[DEBUG] Testing logger import...")
    from bybit_bot.core.logger import TradingBotLogger
    print("[OK] Logger import successful")
    
    print("[DEBUG] Testing bybit_client import...")
    from bybit_bot.exchange.bybit_client import BybitClient
    print("[OK] BybitClient import successful")
    
    print("[DEBUG] Testing hardware monitor import...")
    from bybit_bot.monitoring.hardware_monitor import HardwareMonitor
    print("[OK] HardwareMonitor import successful")
    
except Exception as e:
    print(f"[ERROR] Import failed: {e}")
    print(f"[TRACEBACK] {traceback.format_exc()}")
    sys.exit(1)

print("[SUCCESS] Individual imports test completed successfully!")
