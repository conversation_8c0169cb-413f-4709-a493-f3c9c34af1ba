# PowerShell Profile for E: Drive Conda Environment
# This script sets up the correct conda environment for the Bybit Trading Bot

# Ensure the script runs in the correct directory
Set-Location "E:\The_real_deal_copy\Bybit_Bot\BOT"

# Path to the conda installation on the E: drive - FIXED CORRECT PATH
$condaPath = "E:\conda\miniconda3"

# Path to the conda hook script
$condaHookPath = Join-Path $condaPath "shell\condabin\conda-hook.ps1"

# Check if the conda hook script exists
if (Test-Path $condaHookPath) {
    # Run the conda hook script to enable conda commands
    . $condaHookPath
    Write-Host "[OK] Conda hook loaded from E: drive"
} else {
    Write-Error "[ERROR] conda-hook.ps1 not found at $condaHookPath. Please verify your conda installation path."
    exit 1
}

# Activate the target conda environment
$envName = "bybit-trader"
try {
    conda activate $envName
    Write-Host "[OK] Successfully activated conda environment: $envName"
} catch {
    Write-Error "[ERROR] Failed to activate conda environment '$envName'. Please ensure it exists and is correctly configured."
    Write-Error $_
    exit 1
}

# Set environment variables for optimal performance
$env:PYTHONPATH = "E:\The_real_deal_copy\Bybit_Bot\BOT"
$env:CONDA_DEFAULT_ENV = $envName

# Function to test the environment (can be called from tasks.json)
function Test-Environment {
    Write-Host "[INFO] Testing environment..."
    Write-Host "[INFO] Current Location: $(Get-Location)"
    Write-Host "[INFO] Python Path: $(Get-Command python).Source"
    $pythonVersion = python --version
    Write-Host "[INFO] Python Version: $pythonVersion"
    Write-Host "[INFO] Conda Active Environment: $env:CONDA_DEFAULT_ENV"
    if ($env:CONDA_DEFAULT_ENV -eq $envName) {
        Write-Host "[OK] Correct conda environment is active."
    } else {
        Write-Warning "[WARNING] The active conda environment is not '$envName'."
    }
    Write-Host "[OK] Environment Test Complete."
}

Write-Host "[OK] PowerShell Profile for E: Drive loaded successfully"
