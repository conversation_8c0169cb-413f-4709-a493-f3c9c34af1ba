#!/usr/bin/env python3
"""
FIX DATA CRAWLER API ISSUE
Fixes the data crawler to use proper encrypted credentials
"""

import sys
# Ensure sys.warnoptions exists
if not hasattr(sys, "warnoptions"): sys.warnoptions = []

def fix_market_data_crawler():
    """Fix the market data crawler API initialization"""
    
    file_path = "bybit_bot/data_crawler/market_data_crawler.py"
    
    print("[FIXING] Market Data Crawler API initialization...")
    
    # Read the current file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find and replace the problematic _initialize_exchanges method
    old_method = '''    def _initialize_exchanges(self) -> Dict[str, ccxt.Exchange]:
        """Initialize only the configured exchange (Bybit)"""
        exchanges = {}
        
        # Only initialize Bybit since it's the only configured exchange
        bybit_config = self.config.get_api_key("bybit")
        exchanges['bybit'] = ccxt.bybit({
            'apiKey': bybit_config.get('api_key', '') or '',
            'secret': bybit_config.get('api_secret', '') or '',
            'sandbox': bybit_config.get('testnet', True),
            'enableRateLimit': True,
        })
        
        self.logger.info("Initializing only Bybit exchange (configured exchange)")
        return exchanges'''
    
    new_method = '''    def _initialize_exchanges(self) -> Dict[str, ccxt.Exchange]:
        """Initialize only the configured exchange (Bybit) with secure credentials"""
        exchanges = {}
        
        try:
            # Get encrypted credentials from secure credential manager
            from bybit_bot.security.credential_manager import get_secure_credential
            
            api_key = get_secure_credential('BYBIT_API_KEY')
            api_secret = get_secure_credential('BYBIT_API_SECRET')
            
            if not api_key or not api_secret:
                raise ValueError("Encrypted Bybit API credentials not found")
            
            # Initialize Bybit exchange with live credentials (NO TESTNET)
            exchanges['bybit'] = ccxt.bybit({
                'apiKey': api_key,
                'secret': api_secret,
                'sandbox': False,  # LIVE TRADING ONLY
                'enableRateLimit': True,
                'timeout': 30000,
                'rateLimit': 50,
            })
            
            self.logger.info("[LIVE] Bybit exchange initialized with encrypted credentials")
            return exchanges
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Bybit exchange: {e}")
            # Fallback to config-based credentials
            bybit_config = self.config.get_api_key("bybit")
            if bybit_config:
                exchanges['bybit'] = ccxt.bybit({
                    'apiKey': bybit_config.get('api_key', ''),
                    'secret': bybit_config.get('api_secret', ''),
                    'sandbox': False,  # ALWAYS LIVE
                    'enableRateLimit': True,
                })
                self.logger.warning("[FALLBACK] Using config-based credentials")
            else:
                self.logger.error("[CRITICAL] No Bybit credentials available")
            return exchanges'''
    
    if old_method in content:
        content = content.replace(old_method, new_method)
        
        # Write the fixed content back
        with open(file_path, 'w') as f:
            f.write(content)
        
        print("[OK] Fixed market data crawler API initialization")
        return True
    else:
        print("[ERROR] Could not find the target method to replace")
        return False

def main():
    """Main fix function"""
    print("=" * 60)
    print("FIXING DATA CRAWLER API ISSUES")
    print("=" * 60)
    
    success = fix_market_data_crawler()
    
    if success:
        print("\n[SUCCESS] Data crawler API fixes applied successfully")
        print("[INFO] The data crawler will now use encrypted credentials")
        print("[INFO] Testnet mode disabled - LIVE TRADING ONLY")
    else:
        print("\n[ERROR] Failed to apply fixes")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
