#!/usr/bin/env python3
"""Test BybitClient with fixed credentials"""

import asyncio
import sys
import traceback

# Add current directory to path
sys.path.append('.')

async def test_bybit_client():
    """Test BybitClient initialization and basic operations"""
    try:
        from bybit_bot.core.config import BotConfig
        from bybit_bot.exchange.bybit_client import BybitClient
        
        print('[TEST] Creating BotConfig...')
        config = BotConfig()
        
        print('[TEST] Creating BybitClient...')
        client = BybitClient(config)
        
        print('[TEST] Initializing BybitClient...')
        await client.initialize()
        
        print('[TEST] Testing connection...')
        current_price = await client.get_current_price('BTCUSDT')
        print(f'[TEST] Current BTC price: ${current_price:,.2f}')
        
        print('[TEST] Testing account balance...')
        balance = await client.get_account_balance()
        print(f'[TEST] Account balance loaded: {bool(balance)}')
        if balance:
            print(f"[TEST] Total equity: ${balance.get('total_equity', 0):,.2f}")
            print(f"[TEST] Available balance: ${balance.get('available_balance', 0):,.2f}")
        
        await client.close()
        print('[OK] BybitClient test completed successfully')
        return True
        
    except Exception as e:
        print(f'[ERROR] BybitClient test failed: {e}')
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = asyncio.run(test_bybit_client())
    print(f'\n[BYBIT CLIENT TEST] {"PASSED" if success else "FAILED"}')
