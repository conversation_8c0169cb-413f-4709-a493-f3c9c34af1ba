#!/usr/bin/env python3
"""
SYSTEM INTEGRITY VALIDATOR
Comprehensive validation of all system components, functions, and capabilities
Ensures NO simplification, ALL functions active, complete autonomous operation
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def validate_system_integrity():
    """Comprehensive system integrity validation"""
    print("=" * 80)
    print("SYSTEM INTEGRITY VALIDATION")
    print("=" * 80)
    print(f"Validation Time: {datetime.now().isoformat()}")
    print()
    
    validation_results = {
        "total_checks": 0,
        "passed_checks": 0,
        "failed_checks": 0,
        "warnings": 0,
        "critical_issues": 0,
        "components_validated": [],
        "issues_found": []
    }
    
    try:
        # Import the unified system
        from main_unified_system import UnifiedTradingSystem
        
        print("[INIT] Initializing system for validation...")
        system = UnifiedTradingSystem()
        
        # Initialize the complete system
        await system.initialize_complete_system()
        print("[SUCCESS] System initialization completed!")
        print()
        
        # =====================================
        # CORE SYSTEM VALIDATION
        # =====================================
        
        print("[VALIDATION] Core System Components...")
        core_components = [
            ("Database Manager", system.db_manager),
            ("Hardware Monitor", system.hardware_monitor),
            ("Bybit Client", system.bybit_client),
            ("Bot Manager", system.bot_manager),
            ("Profit Engine", system.profit_engine),
            ("Hyper Profit Engine", system.hyper_profit_engine)
        ]
        
        for name, component in core_components:
            validation_results["total_checks"] += 1
            if component is not None:
                validation_results["passed_checks"] += 1
                validation_results["components_validated"].append(name)
                print(f"   ✅ {name}: ACTIVE")
            else:
                validation_results["failed_checks"] += 1
                validation_results["critical_issues"] += 1
                validation_results["issues_found"].append(f"CRITICAL: {name} is None")
                print(f"   ❌ {name}: INACTIVE")
        
        # =====================================
        # AI SYSTEMS VALIDATION
        # =====================================
        
        print("\n[VALIDATION] AI Systems...")
        ai_components = [
            ("Memory Manager", system.memory_manager),
            ("Meta Cognition", system.meta_cognition),
            ("Code Evolution", system.code_evolution),
            ("Recursive Improvement", system.recursive_improvement),
            ("SuperGPT Integration", system.supergpt_integration)
        ]
        
        for name, component in ai_components:
            validation_results["total_checks"] += 1
            if component is not None:
                validation_results["passed_checks"] += 1
                validation_results["components_validated"].append(name)
                print(f"   ✅ {name}: ACTIVE")
            else:
                validation_results["failed_checks"] += 1
                validation_results["warnings"] += 1
                validation_results["issues_found"].append(f"WARNING: {name} is None")
                print(f"   ⚠️ {name}: INACTIVE")
        
        # =====================================
        # SUPERGPT COMPONENTS VALIDATION
        # =====================================
        
        print("\n[VALIDATION] SuperGPT Components...")
        supergpt_components = [
            ("Self-Healing System", system.self_healing_system),
            ("Autonomy Engine", system.autonomy_engine),
            ("Code Optimizer", system.code_optimizer)
        ]
        
        for name, component in supergpt_components:
            validation_results["total_checks"] += 1
            if component is not None:
                validation_results["passed_checks"] += 1
                validation_results["components_validated"].append(name)
                print(f"   ✅ {name}: ACTIVE")
            else:
                validation_results["failed_checks"] += 1
                validation_results["warnings"] += 1
                validation_results["issues_found"].append(f"WARNING: {name} is None")
                print(f"   ⚠️ {name}: INACTIVE")
        
        # =====================================
        # MCP INTEGRATION VALIDATION
        # =====================================
        
        print("\n[VALIDATION] MCP Integration...")
        mcp_components = [
            ("MCP System", system.mcp_system),
            ("Bybit MCP Manager", system.bybit_mcp_manager),
            ("Optimized MCP Client", system.optimized_mcp_client),
            ("MCP System Initializer", system.mcp_system_initializer),
            ("Advanced MCP Features", system.advanced_mcp_features)
        ]
        
        for name, component in mcp_components:
            validation_results["total_checks"] += 1
            if component is not None:
                validation_results["passed_checks"] += 1
                validation_results["components_validated"].append(name)
                print(f"   ✅ {name}: ACTIVE")
            else:
                validation_results["failed_checks"] += 1
                validation_results["warnings"] += 1
                validation_results["issues_found"].append(f"WARNING: {name} is None")
                print(f"   ⚠️ {name}: INACTIVE")
        
        # =====================================
        # ADVANCED FEATURES VALIDATION
        # =====================================
        
        print("\n[VALIDATION] Advanced Features...")
        advanced_components = [
            ("Multi-Category Manager", system.multi_category_manager),
            ("Profit Monitor", system.profit_monitor),
            ("Agent Orchestrator", system.agent_orchestrator)
        ]
        
        for name, component in advanced_components:
            validation_results["total_checks"] += 1
            if component is not None:
                validation_results["passed_checks"] += 1
                validation_results["components_validated"].append(name)
                print(f"   ✅ {name}: ACTIVE")
            else:
                validation_results["failed_checks"] += 1
                validation_results["warnings"] += 1
                validation_results["issues_found"].append(f"WARNING: {name} is None")
                print(f"   ⚠️ {name}: INACTIVE")
        
        # =====================================
        # DATA CRAWLERS VALIDATION
        # =====================================
        
        print("\n[VALIDATION] Data Crawlers...")
        crawler_components = [
            ("Market Data Crawler", system.market_data_crawler),
            ("News Crawler", system.news_crawler),
            ("Social Crawler", system.social_crawler),
            ("Economic Crawler", system.economic_crawler)
        ]
        
        for name, component in crawler_components:
            validation_results["total_checks"] += 1
            if component is not None:
                validation_results["passed_checks"] += 1
                validation_results["components_validated"].append(name)
                print(f"   ✅ {name}: ACTIVE")
            else:
                validation_results["failed_checks"] += 1
                validation_results["warnings"] += 1
                validation_results["issues_found"].append(f"WARNING: {name} is None")
                print(f"   ⚠️ {name}: INACTIVE")
        
        # =====================================
        # STRATEGY SYSTEMS VALIDATION
        # =====================================
        
        print("\n[VALIDATION] Strategy Systems...")
        strategy_components = [
            ("Strategy Manager", system.strategy_manager),
            ("Adaptive Strategy Engine", system.adaptive_strategy_engine),
            ("Risk Manager", system.risk_manager)
        ]
        
        for name, component in strategy_components:
            validation_results["total_checks"] += 1
            if component is not None:
                validation_results["passed_checks"] += 1
                validation_results["components_validated"].append(name)
                print(f"   ✅ {name}: ACTIVE")
            else:
                validation_results["failed_checks"] += 1
                validation_results["warnings"] += 1
                validation_results["issues_found"].append(f"WARNING: {name} is None")
                print(f"   ⚠️ {name}: INACTIVE")
        
        # =====================================
        # INITIALIZATION PHASES VALIDATION
        # =====================================
        
        print("\n[VALIDATION] Initialization Phases...")
        expected_phases = [
            'logging_setup',
            'database_initialization',
            'hardware_monitoring_setup',
            'ai_systems_initialization',
            'mcp_copilot_integration',
            'supergpt_components_setup',
            'agent_orchestrator_initialization',
            'trading_components_setup',
            'data_crawlers_initialization',
            'strategy_systems_setup',
            'risk_management_setup',
            'performance_monitoring_setup',
            'system_optimization',
            'final_validation'
        ]
        
        completed_phases = getattr(system, 'completed_phases', [])
        
        for phase in expected_phases:
            validation_results["total_checks"] += 1
            if phase in completed_phases:
                validation_results["passed_checks"] += 1
                print(f"   ✅ {phase}: COMPLETED")
            else:
                validation_results["failed_checks"] += 1
                validation_results["warnings"] += 1
                validation_results["issues_found"].append(f"WARNING: Phase {phase} not completed")
                print(f"   ⚠️ {phase}: NOT COMPLETED")
        
        # =====================================
        # FUNCTIONAL VALIDATION
        # =====================================
        
        print("\n[VALIDATION] Functional Tests...")
        
        # Test balance retrieval
        validation_results["total_checks"] += 1
        try:
            if system.bybit_client:
                balance = await system.bybit_client.get_wallet_balance()
                if balance and not balance.get('error'):
                    validation_results["passed_checks"] += 1
                    print(f"   ✅ Balance Retrieval: WORKING")
                else:
                    validation_results["failed_checks"] += 1
                    validation_results["issues_found"].append("WARNING: Balance retrieval failed")
                    print(f"   ⚠️ Balance Retrieval: FAILED")
            else:
                validation_results["failed_checks"] += 1
                validation_results["issues_found"].append("CRITICAL: No Bybit client for balance test")
                print(f"   ❌ Balance Retrieval: NO CLIENT")
        except Exception as e:
            validation_results["failed_checks"] += 1
            validation_results["issues_found"].append(f"ERROR: Balance test failed: {e}")
            print(f"   ❌ Balance Retrieval: ERROR")
        
        # Test price retrieval
        validation_results["total_checks"] += 1
        try:
            if system.bybit_client:
                price = await system.bybit_client.get_current_price('BTCUSDT')
                if price and price > 0:
                    validation_results["passed_checks"] += 1
                    print(f"   ✅ Price Retrieval: WORKING (${price:,.2f})")
                else:
                    validation_results["failed_checks"] += 1
                    validation_results["issues_found"].append("WARNING: Price retrieval failed")
                    print(f"   ⚠️ Price Retrieval: FAILED")
            else:
                validation_results["failed_checks"] += 1
                validation_results["issues_found"].append("CRITICAL: No Bybit client for price test")
                print(f"   ❌ Price Retrieval: NO CLIENT")
        except Exception as e:
            validation_results["failed_checks"] += 1
            validation_results["issues_found"].append(f"ERROR: Price test failed: {e}")
            print(f"   ❌ Price Retrieval: ERROR")
        
        # Test MCP functionality
        validation_results["total_checks"] += 1
        try:
            if system.advanced_mcp_features:
                mcp_performance = await system.advanced_mcp_features.get_mcp_performance_summary()
                if mcp_performance and not mcp_performance.get('error'):
                    validation_results["passed_checks"] += 1
                    print(f"   ✅ MCP Functionality: WORKING")
                else:
                    validation_results["failed_checks"] += 1
                    validation_results["issues_found"].append("WARNING: MCP functionality test failed")
                    print(f"   ⚠️ MCP Functionality: FAILED")
            else:
                validation_results["failed_checks"] += 1
                validation_results["issues_found"].append("WARNING: No MCP features for test")
                print(f"   ⚠️ MCP Functionality: NO FEATURES")
        except Exception as e:
            validation_results["failed_checks"] += 1
            validation_results["issues_found"].append(f"ERROR: MCP test failed: {e}")
            print(f"   ❌ MCP Functionality: ERROR")
        
        # Test multi-category functionality
        validation_results["total_checks"] += 1
        try:
            if system.multi_category_manager:
                analysis = await system.multi_category_manager.analyze_market_conditions('BTCUSDT')
                if analysis and not analysis.get('error'):
                    validation_results["passed_checks"] += 1
                    print(f"   ✅ Multi-Category: WORKING")
                else:
                    validation_results["failed_checks"] += 1
                    validation_results["issues_found"].append("WARNING: Multi-category test failed")
                    print(f"   ⚠️ Multi-Category: FAILED")
            else:
                validation_results["failed_checks"] += 1
                validation_results["issues_found"].append("WARNING: No multi-category manager for test")
                print(f"   ⚠️ Multi-Category: NO MANAGER")
        except Exception as e:
            validation_results["failed_checks"] += 1
            validation_results["issues_found"].append(f"ERROR: Multi-category test failed: {e}")
            print(f"   ❌ Multi-Category: ERROR")
        
        # Test profit monitoring
        validation_results["total_checks"] += 1
        try:
            if system.profit_monitor:
                status = await system.profit_monitor.get_monitoring_status()
                if status and status.get('is_monitoring'):
                    validation_results["passed_checks"] += 1
                    print(f"   ✅ Profit Monitoring: ACTIVE")
                else:
                    validation_results["failed_checks"] += 1
                    validation_results["issues_found"].append("WARNING: Profit monitoring not active")
                    print(f"   ⚠️ Profit Monitoring: INACTIVE")
            else:
                validation_results["failed_checks"] += 1
                validation_results["issues_found"].append("WARNING: No profit monitor")
                print(f"   ⚠️ Profit Monitoring: NO MONITOR")
        except Exception as e:
            validation_results["failed_checks"] += 1
            validation_results["issues_found"].append(f"ERROR: Profit monitoring test failed: {e}")
            print(f"   ❌ Profit Monitoring: ERROR")
        
        # =====================================
        # VALIDATION SUMMARY
        # =====================================
        
        print("\n" + "=" * 80)
        print("VALIDATION SUMMARY")
        print("=" * 80)
        
        success_rate = (validation_results["passed_checks"] / validation_results["total_checks"] * 100) if validation_results["total_checks"] > 0 else 0
        
        print(f"Total Checks: {validation_results['total_checks']}")
        print(f"Passed: {validation_results['passed_checks']}")
        print(f"Failed: {validation_results['failed_checks']}")
        print(f"Warnings: {validation_results['warnings']}")
        print(f"Critical Issues: {validation_results['critical_issues']}")
        print(f"Success Rate: {success_rate:.1f}%")
        print()
        
        print(f"Active Components ({len(validation_results['components_validated'])}):")
        for component in validation_results['components_validated']:
            print(f"   ✅ {component}")
        
        if validation_results['issues_found']:
            print(f"\nIssues Found ({len(validation_results['issues_found'])}):")
            for issue in validation_results['issues_found']:
                print(f"   ⚠️ {issue}")
        
        print("\n" + "=" * 80)
        
        # Determine overall status
        if validation_results['critical_issues'] == 0 and success_rate >= 80:
            print("SYSTEM INTEGRITY: EXCELLENT ✅")
            print("ALL CORE FUNCTIONS ACTIVE - READY FOR MAXIMUM PROFIT GENERATION")
            return True
        elif validation_results['critical_issues'] == 0 and success_rate >= 60:
            print("SYSTEM INTEGRITY: GOOD ⚠️")
            print("CORE FUNCTIONS ACTIVE - PROFIT GENERATION OPERATIONAL")
            return True
        else:
            print("SYSTEM INTEGRITY: NEEDS ATTENTION ❌")
            print("CRITICAL ISSUES DETECTED - REVIEW REQUIRED")
            return False
        
    except Exception as e:
        print(f"[ERROR] System integrity validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        try:
            if 'system' in locals():
                print("\n[CLEANUP] Shutting down system...")
                await system.shutdown()
        except:
            pass

async def main():
    """Main validation function"""
    success = await validate_system_integrity()
    
    if success:
        print("\n" + "=" * 80)
        print("SYSTEM INTEGRITY VALIDATION COMPLETED SUCCESSFULLY!")
        print("BYBIT TRADING SYSTEM: FULLY OPERATIONAL")
        print("ALL FUNCTIONS ACTIVE - MAXIMUM PROFIT MODE ENABLED")
        print("=" * 80)
        sys.exit(0)
    else:
        print("\n" + "=" * 80)
        print("SYSTEM INTEGRITY VALIDATION FAILED!")
        print("REVIEW REQUIRED BEFORE TRADING")
        print("=" * 80)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
