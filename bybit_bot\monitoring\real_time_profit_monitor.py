"""
REAL-TIME PROFIT MONITORING SYSTEM
Continuous profit tracking, balance monitoring, trade confirmation, and automated reinvestment
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import json

logger = logging.getLogger(__name__)

class ProfitEvent(Enum):
    TRADE_EXECUTED = "trade_executed"
    PROFIT_REALIZED = "profit_realized"
    LOSS_REALIZED = "loss_realized"
    BALANCE_UPDATED = "balance_updated"
    REINVESTMENT_TRIGGERED = "reinvestment_triggered"

class AlertLevel(Enum):
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    CRITICAL = "critical"

@dataclass
class ProfitMetrics:
    timestamp: datetime
    total_balance: float
    available_balance: float
    unrealized_pnl: float
    realized_pnl: float
    total_profit: float
    profit_percentage: float
    daily_profit: float
    hourly_profit: float
    trade_count: int
    win_rate: float

@dataclass
class TradeConfirmation:
    trade_id: str
    symbol: str
    side: str
    quantity: float
    price: float
    category: str
    timestamp: datetime
    profit_impact: float
    confirmed: bool

@dataclass
class ProfitAlert:
    alert_id: str
    level: AlertLevel
    message: str
    timestamp: datetime
    metrics: Dict[str, Any]
    action_required: bool

class RealTimeProfitMonitor:
    """Real-time profit monitoring and tracking system"""
    
    def __init__(self, bybit_client, database_manager, config):
        self.bybit_client = bybit_client
        self.db_manager = database_manager
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Monitoring state
        self.is_monitoring = False
        self.monitoring_interval = 5.0  # 5 seconds
        self.last_balance_check = None
        self.initial_balance = 0.0
        self.current_metrics = None
        
        # Profit tracking
        self.profit_history = []
        self.trade_confirmations = []
        self.profit_alerts = []
        
        # Reinvestment settings
        self.auto_reinvestment_enabled = True
        self.reinvestment_threshold = 0.05  # 5% profit threshold
        self.reinvestment_percentage = 0.8  # Reinvest 80% of profits
        
        # Performance tracking
        self.session_start_time = datetime.now()
        self.total_trades_monitored = 0
        self.total_profit_tracked = 0.0
        
    # =====================================
    # REAL-TIME MONITORING
    # =====================================
    
    async def start_monitoring(self) -> None:
        """Start real-time profit monitoring"""
        try:
            self.logger.info("[MONITOR] Starting real-time profit monitoring...")
            
            # Get initial balance
            await self._initialize_baseline_metrics()
            
            # Start monitoring loop
            self.is_monitoring = True
            asyncio.create_task(self._monitoring_loop())
            asyncio.create_task(self._balance_monitoring_loop())
            asyncio.create_task(self._trade_confirmation_loop())
            asyncio.create_task(self._profit_analysis_loop())
            
            self.logger.info("[MONITOR] Real-time profit monitoring ACTIVE")
            
        except Exception as e:
            self.logger.error(f"[MONITOR] Failed to start monitoring: {e}")
            raise
    
    async def stop_monitoring(self) -> None:
        """Stop real-time profit monitoring"""
        try:
            self.is_monitoring = False
            self.logger.info("[MONITOR] Real-time profit monitoring STOPPED")
            
        except Exception as e:
            self.logger.error(f"[MONITOR] Failed to stop monitoring: {e}")
    
    async def _initialize_baseline_metrics(self) -> None:
        """Initialize baseline metrics for profit tracking"""
        try:
            # Get current balance
            balance_data = await self.bybit_client.get_wallet_balance()
            
            if isinstance(balance_data, dict) and 'available_balance' in balance_data:
                self.initial_balance = balance_data['available_balance']
            elif isinstance(balance_data, dict) and 'coins' in balance_data and 'USDT' in balance_data['coins']:
                self.initial_balance = balance_data['coins']['USDT']['available']
            else:
                self.initial_balance = 0.0
            
            self.logger.info(f"[MONITOR] Baseline balance set: ${self.initial_balance:.4f} USDT")
            
            # Initialize metrics
            self.current_metrics = ProfitMetrics(
                timestamp=datetime.now(),
                total_balance=self.initial_balance,
                available_balance=self.initial_balance,
                unrealized_pnl=0.0,
                realized_pnl=0.0,
                total_profit=0.0,
                profit_percentage=0.0,
                daily_profit=0.0,
                hourly_profit=0.0,
                trade_count=0,
                win_rate=0.0
            )
            
        except Exception as e:
            self.logger.error(f"[MONITOR] Baseline initialization failed: {e}")
            self.initial_balance = 0.0
    
    async def _monitoring_loop(self) -> None:
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                # Update profit metrics
                await self._update_profit_metrics()
                
                # Check for profit alerts
                await self._check_profit_alerts()
                
                # Check reinvestment opportunities
                await self._check_reinvestment_opportunities()
                
                # Wait for next cycle
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                self.logger.error(f"[MONITOR] Monitoring loop error: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    async def _balance_monitoring_loop(self) -> None:
        """Dedicated balance monitoring loop"""
        while self.is_monitoring:
            try:
                # Get current balance
                balance_data = await self.bybit_client.get_wallet_balance()
                
                if balance_data and not balance_data.get('error'):
                    await self._process_balance_update(balance_data)
                
                # Wait for next balance check
                await asyncio.sleep(10.0)  # Check balance every 10 seconds
                
            except Exception as e:
                self.logger.error(f"[MONITOR] Balance monitoring error: {e}")
                await asyncio.sleep(10.0)
    
    async def _trade_confirmation_loop(self) -> None:
        """Trade confirmation monitoring loop"""
        while self.is_monitoring:
            try:
                # Check for new trades to confirm
                await self._check_trade_confirmations()
                
                # Wait for next check
                await asyncio.sleep(3.0)  # Check trades every 3 seconds
                
            except Exception as e:
                self.logger.error(f"[MONITOR] Trade confirmation error: {e}")
                await asyncio.sleep(3.0)
    
    async def _profit_analysis_loop(self) -> None:
        """Profit analysis and reporting loop"""
        while self.is_monitoring:
            try:
                # Perform profit analysis
                await self._analyze_profit_performance()
                
                # Generate profit reports
                await self._generate_profit_reports()
                
                # Wait for next analysis
                await asyncio.sleep(60.0)  # Analyze every minute
                
            except Exception as e:
                self.logger.error(f"[MONITOR] Profit analysis error: {e}")
                await asyncio.sleep(60.0)
    
    # =====================================
    # PROFIT TRACKING
    # =====================================
    
    async def _update_profit_metrics(self) -> None:
        """Update current profit metrics"""
        try:
            # Get current balance
            balance_data = await self.bybit_client.get_wallet_balance()
            
            current_balance = 0.0
            if isinstance(balance_data, dict) and 'available_balance' in balance_data:
                current_balance = balance_data['available_balance']
            elif isinstance(balance_data, dict) and 'coins' in balance_data and 'USDT' in balance_data['coins']:
                current_balance = balance_data['coins']['USDT']['available']
            
            # Calculate profit metrics
            total_profit = current_balance - self.initial_balance
            profit_percentage = (total_profit / self.initial_balance * 100) if self.initial_balance > 0 else 0
            
            # Calculate time-based profits
            session_duration = datetime.now() - self.session_start_time
            hours_elapsed = session_duration.total_seconds() / 3600
            
            hourly_profit = total_profit / hours_elapsed if hours_elapsed > 0 else 0
            daily_profit = hourly_profit * 24
            
            # Update metrics
            self.current_metrics = ProfitMetrics(
                timestamp=datetime.now(),
                total_balance=current_balance,
                available_balance=current_balance,
                unrealized_pnl=0.0,  # Would need position data
                realized_pnl=total_profit,
                total_profit=total_profit,
                profit_percentage=profit_percentage,
                daily_profit=daily_profit,
                hourly_profit=hourly_profit,
                trade_count=self.total_trades_monitored,
                win_rate=0.0  # Would need trade outcome data
            )
            
            # Store metrics history
            self.profit_history.append(self.current_metrics)
            
            # Keep only last 1000 entries
            if len(self.profit_history) > 1000:
                self.profit_history = self.profit_history[-1000:]
            
        except Exception as e:
            self.logger.error(f"[MONITOR] Profit metrics update failed: {e}")
    
    async def _process_balance_update(self, balance_data: Dict[str, Any]) -> None:
        """Process balance update and detect changes"""
        try:
            current_balance = 0.0
            if isinstance(balance_data, dict) and 'available_balance' in balance_data:
                current_balance = balance_data['available_balance']
            elif isinstance(balance_data, dict) and 'coins' in balance_data and 'USDT' in balance_data['coins']:
                current_balance = balance_data['coins']['USDT']['available']
            
            # Check for significant balance changes
            if self.current_metrics:
                balance_change = current_balance - self.current_metrics.available_balance
                
                if abs(balance_change) > 0.01:  # $0.01 threshold
                    await self._create_profit_alert(
                        AlertLevel.INFO,
                        f"Balance changed by ${balance_change:.4f} USDT",
                        {"balance_change": balance_change, "new_balance": current_balance}
                    )
            
            self.last_balance_check = datetime.now()
            
        except Exception as e:
            self.logger.error(f"[MONITOR] Balance update processing failed: {e}")
    
    async def _check_trade_confirmations(self) -> None:
        """Check for trade confirmations"""
        try:
            # In a real implementation, this would check for new trades
            # For now, we'll simulate trade confirmation checking
            pass
            
        except Exception as e:
            self.logger.error(f"[MONITOR] Trade confirmation check failed: {e}")
    
    async def _check_profit_alerts(self) -> None:
        """Check for profit-related alerts"""
        try:
            if not self.current_metrics:
                return
            
            # Check for profit milestones
            if self.current_metrics.total_profit > 0:
                if self.current_metrics.profit_percentage >= 10.0:  # 10% profit
                    await self._create_profit_alert(
                        AlertLevel.SUCCESS,
                        f"Excellent profit achieved: {self.current_metrics.profit_percentage:.2f}%",
                        {"profit": self.current_metrics.total_profit}
                    )
                elif self.current_metrics.profit_percentage >= 5.0:  # 5% profit
                    await self._create_profit_alert(
                        AlertLevel.SUCCESS,
                        f"Good profit achieved: {self.current_metrics.profit_percentage:.2f}%",
                        {"profit": self.current_metrics.total_profit}
                    )
            
            # Check for loss alerts
            if self.current_metrics.total_profit < -5.0:  # $5 loss
                await self._create_profit_alert(
                    AlertLevel.WARNING,
                    f"Loss detected: ${abs(self.current_metrics.total_profit):.2f}",
                    {"loss": self.current_metrics.total_profit}
                )
            
        except Exception as e:
            self.logger.error(f"[MONITOR] Profit alerts check failed: {e}")
    
    async def _check_reinvestment_opportunities(self) -> None:
        """Check for automated reinvestment opportunities"""
        try:
            if not self.auto_reinvestment_enabled or not self.current_metrics:
                return
            
            # Check if profit threshold is met
            if self.current_metrics.profit_percentage >= (self.reinvestment_threshold * 100):
                reinvestment_amount = self.current_metrics.total_profit * self.reinvestment_percentage
                
                if reinvestment_amount >= 5.0:  # Minimum $5 reinvestment
                    await self._trigger_reinvestment(reinvestment_amount)
            
        except Exception as e:
            self.logger.error(f"[MONITOR] Reinvestment check failed: {e}")
    
    async def _trigger_reinvestment(self, amount: float) -> None:
        """Trigger automated profit reinvestment"""
        try:
            self.logger.info(f"[REINVEST] Triggering reinvestment of ${amount:.2f}")
            
            # Create reinvestment alert
            await self._create_profit_alert(
                AlertLevel.INFO,
                f"Automated reinvestment triggered: ${amount:.2f}",
                {"reinvestment_amount": amount, "trigger": "profit_threshold"}
            )
            
            # In a real implementation, this would execute a reinvestment trade
            # For now, we'll just log the event
            
        except Exception as e:
            self.logger.error(f"[REINVEST] Reinvestment trigger failed: {e}")
    
    async def _create_profit_alert(self, level: AlertLevel, message: str, 
                                  metrics: Dict[str, Any]) -> None:
        """Create a profit-related alert"""
        try:
            alert = ProfitAlert(
                alert_id=f"alert_{int(datetime.now().timestamp())}",
                level=level,
                message=message,
                timestamp=datetime.now(),
                metrics=metrics,
                action_required=level in [AlertLevel.WARNING, AlertLevel.CRITICAL]
            )
            
            self.profit_alerts.append(alert)
            
            # Keep only last 100 alerts
            if len(self.profit_alerts) > 100:
                self.profit_alerts = self.profit_alerts[-100:]
            
            # Log alert
            log_level = {
                AlertLevel.INFO: self.logger.info,
                AlertLevel.SUCCESS: self.logger.info,
                AlertLevel.WARNING: self.logger.warning,
                AlertLevel.CRITICAL: self.logger.error
            }[level]
            
            log_level(f"[ALERT] {message}")
            
        except Exception as e:
            self.logger.error(f"[MONITOR] Alert creation failed: {e}")
    
    # =====================================
    # ANALYSIS AND REPORTING
    # =====================================
    
    async def _analyze_profit_performance(self) -> None:
        """Analyze profit performance trends"""
        try:
            if len(self.profit_history) < 2:
                return
            
            # Calculate performance trends
            recent_metrics = self.profit_history[-10:]  # Last 10 data points
            
            # Analyze profit trend
            profit_trend = "stable"
            if len(recent_metrics) >= 2:
                first_profit = recent_metrics[0].total_profit
                last_profit = recent_metrics[-1].total_profit
                
                if last_profit > first_profit * 1.02:  # 2% increase
                    profit_trend = "increasing"
                elif last_profit < first_profit * 0.98:  # 2% decrease
                    profit_trend = "decreasing"
            
            # Store analysis results
            analysis = {
                "timestamp": datetime.now().isoformat(),
                "profit_trend": profit_trend,
                "data_points": len(recent_metrics),
                "current_profit": self.current_metrics.total_profit if self.current_metrics else 0
            }
            
        except Exception as e:
            self.logger.error(f"[MONITOR] Profit analysis failed: {e}")
    
    async def _generate_profit_reports(self) -> None:
        """Generate profit reports"""
        try:
            if not self.current_metrics:
                return
            
            # Generate summary report
            report = {
                "timestamp": datetime.now().isoformat(),
                "session_duration": str(datetime.now() - self.session_start_time),
                "initial_balance": self.initial_balance,
                "current_balance": self.current_metrics.total_balance,
                "total_profit": self.current_metrics.total_profit,
                "profit_percentage": self.current_metrics.profit_percentage,
                "hourly_profit_rate": self.current_metrics.hourly_profit,
                "daily_profit_projection": self.current_metrics.daily_profit,
                "trades_monitored": self.total_trades_monitored,
                "alerts_generated": len(self.profit_alerts)
            }
            
            # Log periodic report
            if datetime.now().minute % 5 == 0:  # Every 5 minutes
                self.logger.info(f"[REPORT] Profit: ${report['total_profit']:.4f} ({report['profit_percentage']:.2f}%)")
            
        except Exception as e:
            self.logger.error(f"[MONITOR] Report generation failed: {e}")
    
    # =====================================
    # PUBLIC INTERFACE
    # =====================================
    
    async def get_current_metrics(self) -> Optional[ProfitMetrics]:
        """Get current profit metrics"""
        return self.current_metrics
    
    async def get_profit_history(self, limit: int = 100) -> List[ProfitMetrics]:
        """Get profit history"""
        return self.profit_history[-limit:] if self.profit_history else []
    
    async def get_recent_alerts(self, limit: int = 20) -> List[ProfitAlert]:
        """Get recent profit alerts"""
        return self.profit_alerts[-limit:] if self.profit_alerts else []
    
    async def get_monitoring_status(self) -> Dict[str, Any]:
        """Get monitoring system status"""
        return {
            "is_monitoring": self.is_monitoring,
            "session_start": self.session_start_time.isoformat(),
            "initial_balance": self.initial_balance,
            "monitoring_interval": self.monitoring_interval,
            "auto_reinvestment": self.auto_reinvestment_enabled,
            "total_trades_monitored": self.total_trades_monitored,
            "alerts_count": len(self.profit_alerts),
            "history_count": len(self.profit_history)
        }
