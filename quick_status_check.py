#!/usr/bin/env python3
"""
QUICK SYSTEM STATUS CHECK
Non-intrusive check of trading system status while it's running
"""

import sqlite3
import json
from datetime import datetime
from pathlib import Path

def check_system_status():
    """Check system status without interfering with running processes"""
    status = {
        'timestamp': datetime.now().isoformat(),
        'database': {},
        'config_files': {},
        'api_status': {},
        'overall_health': 'unknown'
    }
    
    # Check database
    db_path = 'bybit_trading_bot.db'
    if Path(db_path).exists():
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            status['database']['tables_count'] = len(tables)
            status['database']['status'] = 'connected'
            
            # Check for recent activity
            if 'trades' in tables:
                cursor.execute("SELECT COUNT(*) FROM trades")
                total_trades = cursor.fetchone()[0]
                status['database']['total_trades'] = total_trades
                
                # Check for recent trades (last hour)
                cursor.execute("""
                    SELECT COUNT(*) FROM trades 
                    WHERE datetime(timestamp) > datetime('now', '-1 hour')
                """)
                recent_trades = cursor.fetchone()[0]
                status['database']['recent_trades'] = recent_trades
            
            if 'positions' in tables:
                cursor.execute("SELECT COUNT(*) FROM positions WHERE status = 'open'")
                open_positions = cursor.fetchone()[0]
                status['database']['open_positions'] = open_positions
                
            conn.close()
            
        except Exception as e:
            status['database']['status'] = f'error: {str(e)}'
    else:
        status['database']['status'] = 'file_not_found'
    
    # Check config files
    config_files = [
        'validated_symbols.json',
        'symbol_config.json', 
        'risk_config.json',
        'websocket_config.json'
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            try:
                with open(config_file, 'r') as f:
                    data = json.load(f)
                status['config_files'][config_file] = 'valid'
            except:
                status['config_files'][config_file] = 'invalid'
        else:
            status['config_files'][config_file] = 'missing'
    
    # Check API validation
    if Path('validated_symbols.json').exists():
        with open('validated_symbols.json', 'r') as f:
            api_data = json.load(f)
            status['api_status'] = {
                'valid_symbols': len(api_data.get('valid_symbols', [])),
                'api_status': api_data.get('api_status', 'unknown'),
                'last_validated': api_data.get('test_time', 0)
            }
    
    # Determine overall health
    healthy_indicators = 0
    total_indicators = 4
    
    if status['database'].get('status') == 'connected':
        healthy_indicators += 1
    
    if status['config_files'].get('validated_symbols.json') == 'valid':
        healthy_indicators += 1
        
    if status['api_status'].get('api_status') == 'operational':
        healthy_indicators += 1
        
    if status['api_status'].get('valid_symbols', 0) >= 5:
        healthy_indicators += 1
    
    health_percentage = (healthy_indicators / total_indicators) * 100
    
    if health_percentage >= 75:
        status['overall_health'] = 'healthy'
    elif health_percentage >= 50:
        status['overall_health'] = 'warning'
    else:
        status['overall_health'] = 'critical'
    
    return status

def main():
    """Main status check"""
    print("=== TRADING SYSTEM STATUS CHECK ===")
    print("(Non-intrusive check while system is running)")
    print()
    
    status = check_system_status()
    
    # Display results
    print(f"Overall Health: {status['overall_health'].upper()}")
    print(f"Check Time: {status['timestamp']}")
    print()
    
    print("DATABASE STATUS:")
    print(f"  Status: {status['database'].get('status', 'unknown')}")
    print(f"  Tables: {status['database'].get('tables_count', 0)}")
    print(f"  Total Trades: {status['database'].get('total_trades', 0)}")
    print(f"  Recent Trades: {status['database'].get('recent_trades', 0)}")
    print(f"  Open Positions: {status['database'].get('open_positions', 0)}")
    print()
    
    print("API STATUS:")
    print(f"  Valid Symbols: {status['api_status'].get('valid_symbols', 0)}")
    print(f"  API Status: {status['api_status'].get('api_status', 'unknown')}")
    print()
    
    print("CONFIG FILES:")
    for config, stat in status['config_files'].items():
        print(f"  {config}: {stat}")
    print()
    
    # Save status
    with open('quick_status_check.json', 'w') as f:
        json.dump(status, f, indent=2)
    
    print("Status saved to: quick_status_check.json")
    
    return status

if __name__ == "__main__":
    main()
