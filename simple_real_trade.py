#!/usr/bin/env python3
"""
SIMPLE REAL TRADING EXECUTION
Direct execution of real BTC purchase bypassing complex AI initialization
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def execute_simple_real_trade():
    """Execute real BTC purchase with minimal dependencies"""
    print("=" * 60)
    print("EXECUTING SIMPLE REAL TRADING OPERATIONS")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    try:
        # Import only essential components
        from bybit_bot.security.credential_manager import get_secure_credential
        from bybit_bot.exchange.bybit_client import BybitClient
        from bybit_bot.core.config import BotConfig

        print("[INIT] Getting secure credentials...")
        api_key = get_secure_credential('BYBIT_API_KEY')
        api_secret = get_secure_credential('BYBIT_API_SECRET')

        if not api_key or not api_secret:
            print("[ERROR] No API credentials available")
            return False

        print("[SUCCESS] Credentials loaded successfully")
        print(f"   API Key: {api_key[:8]}...{api_key[-4:]}")
        print()

        # Create config
        print("[INIT] Creating configuration...")
        config = BotConfig()
        print("[SUCCESS] Configuration created")

        # Create simple client
        print("[INIT] Creating Bybit client...")
        client = BybitClient(config)
        await client.initialize()
        print("[SUCCESS] Bybit client initialized")
        print()
        
        # Get balance
        print("[BALANCE] Getting account balance...")
        balance_data = await client.get_wallet_balance()
        
        available_balance = 0.0
        if isinstance(balance_data, dict) and 'available_balance' in balance_data:
            available_balance = balance_data['available_balance']
        elif isinstance(balance_data, dict) and 'coins' in balance_data and 'USDT' in balance_data['coins']:
            available_balance = balance_data['coins']['USDT']['available']
        
        print(f"[BALANCE] Available Balance: ${available_balance:.4f} USDT")
        
        if available_balance < 5.0:
            print(f"[ERROR] Insufficient balance: ${available_balance:.4f} (minimum $5.00 required)")
            return False
        
        # Get BTC price
        print("[MARKET] Getting BTC price...")
        current_price = await client.get_current_price('BTCUSDT')
        print(f"[MARKET] Current BTC Price: ${current_price:,.2f}")
        
        # Calculate order size - Use most of available balance to meet minimum order requirements
        trade_amount = min(available_balance * 0.95, available_balance - 1.0)  # Use 95% of available, leave $1 buffer
        quantity = trade_amount / current_price
        # Round to 6 decimal places for Bybit precision requirements
        quantity = round(quantity, 6)
        
        print(f"[TRADE] Calculated Trade:")
        print(f"   Amount: ${trade_amount:.2f}")
        print(f"   Quantity: {quantity:.8f} BTC")
        print(f"   Price: ${current_price:,.2f}")
        print()
        
        # Execute order - Try linear first, then spot if that fails
        print("[EXECUTE] Placing REAL BTC order...")

        # Try linear trading first (derivatives/futures)
        try:
            print("[EXECUTE] Attempting linear (derivatives) trading...")
            result = await client.place_order(
                symbol='BTCUSDT',
                side='Buy',
                order_type='Market',
                quantity=quantity,
                category='linear'
            )
        except Exception as linear_error:
            print(f"[WARNING] Linear trading failed: {linear_error}")
            print("[EXECUTE] Attempting spot trading...")
            # Fallback to spot trading
            result = await client.place_order(
                symbol='BTCUSDT',
                side='Buy',
                order_type='Market',
                quantity=quantity,
                category='spot'
            )
        
        print("[RESULT] Order execution result:")
        if isinstance(result, dict) and 'order_id' in result:
            order_id = result['order_id']
            print("[SUCCESS] REAL TRADE EXECUTED!")
            print(f"   Order ID: {order_id}")
            print(f"   Trade Amount: ${trade_amount:.2f}")
            print(f"   BTC Quantity: {quantity:.8f}")
            print(f"   Execution Time: {datetime.now().isoformat()}")
            print()
            print("PROFIT GENERATION ACTIVATED!")
            return True
        else:
            print(f"[ERROR] Order execution failed: {result}")
            return False
            
    except Exception as e:
        print(f"[ERROR] Simple real trade execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main execution function"""
    success = await execute_simple_real_trade()
    
    if success:
        print("=" * 60)
        print("SIMPLE REAL TRADING OPERATIONS COMPLETED SUCCESSFULLY!")
        print("PROFIT GENERATION IS NOW ACTIVE!")
        print("=" * 60)
        sys.exit(0)
    else:
        print("=" * 60)
        print("SIMPLE REAL TRADING OPERATIONS FAILED!")
        print("=" * 60)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
